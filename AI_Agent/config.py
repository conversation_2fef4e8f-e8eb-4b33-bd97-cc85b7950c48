"""
应用配置文件 - 包含常量、配置项和全局设置
"""
import os

# 支持的文件类型
ALLOWED_FILE_TYPES = [
    ".txt", ".docx", ".pdf", ".xlsx", ".epub", ".mobi", ".md", ".csv",
    ".bmp", ".png", ".jpg", ".jpeg", ".gif", ".tsv"
]

# 服务器配置
DEFAULT_HOST = "127.0.0.1"
DEFAULT_PORT = 12128

# 缓存配置
DEFAULT_MAX_CACHE_SIZE = 50  # 最多缓存50个对话
DEFAULT_CACHE_TTL = 1800     # 30分钟无访问后清理

# 文件路径配置
STATIC_DIR = "static"
FILES_DIR = os.path.join(STATIC_DIR, "files")
IMAGES_DIR = os.path.join(STATIC_DIR, "images")

# 数据库配置
DEFAULT_DB_PATH = "./database/chat_history.db"

# matplotlib配置
import matplotlib
matplotlib.use('Agg') 