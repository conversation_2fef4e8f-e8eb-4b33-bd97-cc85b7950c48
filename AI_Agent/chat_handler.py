"""
聊天处理模块 - 专门处理Agent响应生成和聊天流逻辑
"""
import os
import pandas as pd
from typing import List

from models import ChatMessage
from utils import red_log, get_image_path


class ChatHandler:
    """聊天处理器 - 处理Agent响应生成等聊天相关逻辑"""
    
    def __init__(self, memory_manager, agent_manager):
        """
        初始化聊天处理器
        
        Args:
            memory_manager: 内存管理器实例
            agent_manager: Agent管理器实例
        """
        self.memory_manager = memory_manager
        self.agent_manager = agent_manager
    
    def handle_reset_commands(self, message: ChatMessage, user_id: str, message_id: str):
        """
        处理重置命令（清除历史代码、图片缓存等）
        
        Args:
            message: 聊天消息
            user_id: 用户ID
            message_id: 消息ID
        """
        # 检查用户是否要求全新绘图（清除历史代码的关键词）
        reset_keywords = ["重新绘图", "从头开始", "全新绘图", "重新画", "重新生成", "新的图表", "清除历史"]
        # 检查是否要求重新分析图片样式的关键词
        reset_image_keywords = ["重新分析图片", "重新分析样式", "重新识别图片"]
        
        user_message_lower = message.message.lower()
        should_reset_code = any(keyword in user_message_lower for keyword in reset_keywords)
        should_reset_image_cache = any(keyword in user_message_lower for keyword in reset_image_keywords)
        
        if should_reset_code:
            self.memory_manager.clear_latest_plotting_code(user_id, message_id)
            print(f"🔄 检测到重新绘图关键词，已清除用户 {user_id}:{message_id} 的历史代码")
        
        if should_reset_image_cache:
            self.memory_manager.clear_image_analysis_cache(user_id, message_id)
            print(f"🔄 检测到重新分析图片关键词，已清除用户 {user_id}:{message_id} 的图片缓存和样式参考")
        
        return should_reset_code
    
    def get_context_history(self, user_id: str, message_id: str, limit: int = 5) -> List[str]:
        """
        获取用户的对话历史上下文
        
        Args:
            user_id: 用户ID
            message_id: 消息ID
            limit: 限制数量
            
        Returns:
            List[str]: 历史消息列表
        """
        try:
            # 从数据库获取最近的对话消息
            messages = self.memory_manager.db.load_messages_for_api(user_id, message_id)
            
            # 只保留用户消息，排除系统消息，取最近的几条
            user_messages = [
                msg['content'] for msg in messages[-limit:] 
                if msg['role'] == 'human' and msg['content'].strip()
            ]
            
            return user_messages
        except Exception as e:
            print(f"⚠️ 获取对话历史失败: {str(e)}")
            return []
    
    def get_dataframe_preview(self, file_paths: List[str]) -> str:
        """
        获取数据文件的预览信息供Agent使用
        
        Args:
            file_paths: 文件路径列表
            
        Returns:
            str: 数据预览信息
        """
        if not file_paths:
            return ""
        
        preview_parts = []
        for file_path in file_paths:
            try:
                if file_path.lower().endswith(('.csv', '.tsv', '.txt')):
                    df = pd.read_csv(file_path)
                    file_name = os.path.basename(file_path)
                    preview = f"文件: {file_name}\n形状: {df.shape}\n前几行:\n{df.head().to_string()}\n"
                    preview_parts.append(preview)
            except Exception as e:
                preview_parts.append(f"文件: {os.path.basename(file_path)} - 读取失败: {str(e)}")
        
        return "\n".join(preview_parts) if preview_parts else ""
    
    async def generate_agent_manager_response(self, message: ChatMessage, session_id: str, has_files: bool, context: dict):
        """
        使用Agent管理器生成聊天响应的异步生成器
        
        Args:
            message: 聊天消息
            session_id: 会话ID
            has_files: 是否有文件
            context: 上下文信息
            
        Yields:
            str: 响应片段
        """
        try:
            # 从session_id解析用户信息
            user_id = context['user_id']
            message_id = context['message_id']
            # 保存用户消息到数据库
            self.memory_manager.save_message_to_db(user_id, message_id, "human", message.message)
            # 红色日志标识LLM调用
            red_log(f"🚀 Agent管理器处理请求 - Session: {session_id}")

            # 使用Agent管理器路由请求
            result = self.agent_manager.route_request(
                user_input=message.message,
                session_id=session_id,
                user_id=user_id,
                has_files=has_files,
                context=context
            )
            
            if not result['success']:
                # 处理错误情况
                error_response = result['output']
                yield error_response
                self.memory_manager.save_message_to_db(user_id, message_id, "ai", error_response)
                return
            
            # 获取Agent响应
            final_output = result['output']
            metadata = result.get('metadata', {})
            
            # 保存意图信息到用户状态
            user_state = self.memory_manager.get_user_state(user_id, message_id)
            user_state['last_intent'] = metadata.get('intent')
            user_state['intent_confidence'] = metadata.get('confidence', 0.0)
            user_state['last_agent'] = metadata.get('agent_name')
            
            print(f"🤖 Agent处理完成: {metadata.get('agent_name')} (意图: {metadata.get('intent')}, 置信度: {metadata.get('confidence', 0):.2f})")
            
            # 处理绘图代码保存
            if result.get('plotting_code'):
                self.memory_manager.update_latest_plotting_code(user_id, message_id, result['plotting_code'])
                print(f"✅ 成功保存绘图代码到用户状态: {user_id}:{message_id}")
            
            # 流式返回最终输出
            for chunk in final_output:
                yield chunk
            
            # 检查是否生成了图像
            image_path = context.get('image_path')
            generated_image_path = None
            if image_path and os.path.exists(image_path):
                generated_image_path = image_path
                # 返回图像URL（使用相对于static的路径）
                relative_path = os.path.relpath(image_path, "static")
                image_url = f"/static/{relative_path}"
                yield f"\n执行结果：\n{'执行成功'}\n[生成的图像]({image_url})"
            
            # 添加智能建议（如果有）
            if result.get('suggestions'):
                suggestions_text = "\n\n💡 **建议操作**：\n" + "\n".join(f"- {s}" for s in result['suggestions'])
                yield suggestions_text
            
            if result.get('visualization_suggestions'):
                viz_suggestions = "\n\n📊 **可视化建议**：\n" + "\n".join(f"- {s}" for s in result['visualization_suggestions'])
                yield viz_suggestions
            
            # 保存AI响应到数据库，包含图像路径（如果有的话）
            self.memory_manager.save_message_to_db(user_id, message_id, "ai", final_output, None, generated_image_path)
                
        except Exception as e:
            print(f"Agent管理器执行错误: {str(e)}")
            error_msg = f"执行过程中出现错误: {str(e)}"
            yield error_msg
            
            # 尝试保存错误信息
            try:
                user_id, message_id = session_id.split(':', 1)
                self.memory_manager.save_message_to_db(user_id, message_id, "ai", error_msg)
            except:
                pass 