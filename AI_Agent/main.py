"""
主应用入口 - 简化版main.py
只包含FastAPI应用初始化、路由注册和启动逻辑
"""
from fastapi import FastAPI
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse

from api_routes import setup_routes
from config import DEFAULT_HOST, DEFAULT_PORT, STATIC_DIR

# 创建FastAPI应用实例
app = FastAPI()

# 挂载静态文件服务
app.mount("/static", StaticFiles(directory=STATIC_DIR), name="static")

# 注册所有API路由
setup_routes(app)

@app.get("/")
async def read_index():
    """首页路由"""
    return FileResponse("static/index_.html")

if __name__ == "__main__":
    import uvicorn
    # 启动应用服务器
    uvicorn.run(app, host=DEFAULT_HOST, port=DEFAULT_PORT)