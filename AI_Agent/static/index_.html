<!DOCTYPE html>
<html lang="zh">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>迈维AI智能助手</title>
  
  <!-- Favicon 配置 -->
  <link rel="icon" type="image/png" sizes="32x32" href="/static/logo.png">
  <link rel="icon" type="image/png" sizes="16x16" href="/static/logo.png">
  <link rel="shortcut icon" href="/static/logo.png">
  <link rel="apple-touch-icon" sizes="180x180" href="/static/logo.png">
  <meta name="msapplication-TileImage" content="/static/logo.png">
  <meta name="theme-color" content="#4facfe">
  
  <link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.min.css">
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      padding: 20px;
      color: #333;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      background: rgba(255, 255, 255, 0.95);
      border-radius: 20px;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
      backdrop-filter: blur(10px);
      overflow: hidden;
      animation: slideUp 0.8s ease-out;
      display: flex;
      transition: all 0.3s ease;
    }

    @keyframes slideUp {
      from {
        opacity: 0;
        transform: translateY(30px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .sidebar {
      width: 320px;
      background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
      display: flex;
      flex-direction: column;
      border-radius: 20px 0 0 20px;
      transition: all 0.3s ease;
    }

    .sidebar-header {
      padding: 20px;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      background: rgba(0, 0, 0, 0.1);
    }

    .sidebar-title {
      color: white;
      font-size: 1.2em;
      font-weight: 600;
      margin-bottom: 15px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 10px;
    }

    .collapse-button {
      background: none;
      border: none;
      color: rgba(255, 255, 255, 0.8);
      cursor: pointer;
      padding: 5px;
      border-radius: 4px;
      transition: all 0.2s ease;
      font-size: 16px;
    }

    .collapse-button:hover {
      background: rgba(255, 255, 255, 0.1);
      color: white;
    }

    .sidebar.collapsed {
      width: 60px;
    }

    .sidebar.collapsed .sidebar-header {
      padding: 20px 10px;
    }

    .sidebar.collapsed .sidebar-title span,
    .sidebar.collapsed .search-box,
    .sidebar.collapsed .chat-item-title,
    .sidebar.collapsed .chat-item-preview,
    .sidebar.collapsed .chat-item-time {
      display: none;
    }

    .sidebar.collapsed .chat-item {
      padding: 15px 10px;
      margin-bottom: 8px;
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 50px;
    }

    .sidebar.collapsed .chat-item::before {
      content: '💬';
      font-size: 20px;
    }

    .sidebar.collapsed .chat-item {
      position: relative;
    }

    .sidebar.collapsed .chat-item:hover::after {
      content: attr(data-title);
      position: absolute;
      left: 100%;
      top: 50%;
      transform: translateY(-50%);
      margin-left: 10px;
      background: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 5px 10px;
      border-radius: 5px;
      font-size: 12px;
      white-space: nowrap;
      z-index: 1000;
      pointer-events: none;
    }

    .search-box {
      width: 100%;
      padding: 10px 15px;
      border: none;
      border-radius: 20px;
      background: rgba(255, 255, 255, 0.1);
      color: white;
      font-size: 14px;
      outline: none;
      transition: all 0.3s ease;
    }

    .search-box::placeholder {
      color: rgba(255, 255, 255, 0.6);
    }

    .search-box:focus {
      background: rgba(255, 255, 255, 0.2);
      box-shadow: 0 0 0 2px rgba(79, 172, 254, 0.3);
    }

    .chat-list {
      flex: 1;
      overflow-y: auto;
      padding: 10px;
    }

    .chat-list::-webkit-scrollbar {
      width: 6px;
    }

    .chat-list::-webkit-scrollbar-track {
      background: rgba(255, 255, 255, 0.1);
      border-radius: 3px;
    }

    .chat-list::-webkit-scrollbar-thumb {
      background: rgba(255, 255, 255, 0.3);
      border-radius: 3px;
    }

    .chat-list::-webkit-scrollbar-thumb:hover {
      background: rgba(255, 255, 255, 0.5);
    }

    .chat-item {
      padding: 15px;
      margin-bottom: 8px;
      border-radius: 12px;
      cursor: pointer;
      transition: all 0.3s ease;
      border: 1px solid transparent;
      background: rgba(255, 255, 255, 0.05);
    }

    .chat-item:hover {
      background: rgba(255, 255, 255, 0.1);
      transform: translateX(5px);
    }

    .chat-item.active {
      background: rgba(79, 172, 254, 0.2);
      border-color: rgba(79, 172, 254, 0.5);
    }

    .chat-item-title {
      color: white;
      font-weight: 600;
      font-size: 14px;
      margin-bottom: 5px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .chat-item-preview {
      color: rgba(255, 255, 255, 0.7);
      font-size: 12px;
      line-height: 1.4;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }

    .chat-item-time {
      color: rgba(255, 255, 255, 0.5);
      font-size: 11px;
    }

    .chat-item-delete {
      color: rgba(255, 255, 255, 0.5);
      cursor: pointer;
      opacity: 0;
      transition: all 0.2s ease;
    }

    .chat-item:hover .chat-item-delete {
      opacity: 1;
    }

    .chat-item-delete:hover {
      color: #ff6b6b;
    }

    .main-area {
      flex: 1;
      display: flex;
      flex-direction: column;
    }

    .header {
      background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      padding: 25px 30px;
      text-align: center;
      position: relative;
      overflow: hidden;
    }

    .header::before {
      content: '';
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: repeating-linear-gradient(
        45deg,
        transparent,
        transparent 10px,
        rgba(255, 255, 255, 0.1) 10px,
        rgba(255, 255, 255, 0.1) 20px
      );
      animation: shimmer 20s linear infinite;
    }

    @keyframes shimmer {
      0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
      100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
    }

    .header h1 {
      color: white;
      font-size: 2.2em;
      font-weight: 600;
      margin: 0;
      position: relative;
      z-index: 1;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }

    .header .subtitle {
      color: rgba(255, 255, 255, 0.9);
      font-size: 1em;
      margin-top: 8px;
      position: relative;
      z-index: 1;
    }

    /* 用户信息样式 */
    .user-info {
      position: absolute;
      top: 20px;
      right: 30px;
      display: flex;
      align-items: center;
      gap: 10px;
      z-index: 2;
    }

    .user-display {
      display: flex;
      align-items: center;
      gap: 8px;
      background: rgba(255, 255, 255, 0.2);
      padding: 8px 15px;
      border-radius: 20px;
      color: white;
      font-weight: 500;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.3);
    }

    .user-display i {
      font-size: 18px;
      color: rgba(255, 255, 255, 0.9);
    }

    .logout-btn {
      background: none;
      border: none;
      color: rgba(255, 255, 255, 0.8);
      cursor: pointer;
      padding: 4px;
      border-radius: 4px;
      transition: all 0.2s ease;
      margin-left: 5px;
    }

    .logout-btn:hover {
      background: rgba(255, 255, 255, 0.2);
      color: white;
    }

    .login-btn {
      background: rgba(255, 255, 255, 0.2);
      border: 1px solid rgba(255, 255, 255, 0.3);
      color: white;
      padding: 8px 15px;
      border-radius: 20px;
      cursor: pointer;
      font-weight: 500;
      transition: all 0.3s ease;
      backdrop-filter: blur(10px);
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .login-btn:hover {
      background: rgba(255, 255, 255, 0.3);
      transform: translateY(-1px);
      box-shadow: 0 4px 15px rgba(255, 255, 255, 0.2);
    }

    /* 登录模态框样式 */
    .login-modal {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.6);
      z-index: 2000;
      backdrop-filter: blur(5px);
    }

    .login-modal-content {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: white;
      border-radius: 20px;
      overflow: hidden;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
      width: 90%;
      max-width: 400px;
      animation: modalSlideIn 0.3s ease-out;
    }

    @keyframes modalSlideIn {
      from {
        opacity: 0;
        transform: translate(-50%, -60%);
      }
      to {
        opacity: 1;
        transform: translate(-50%, -50%);
      }
    }

    .login-header {
      background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      color: white;
      padding: 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .login-header h3 {
      margin: 0;
      font-size: 1.3em;
      font-weight: 600;
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .close-btn {
      background: none;
      border: none;
      color: rgba(255, 255, 255, 0.8);
      cursor: pointer;
      padding: 8px;
      border-radius: 8px;
      transition: all 0.2s ease;
      font-size: 16px;
    }

    .close-btn:hover {
      background: rgba(255, 255, 255, 0.2);
      color: white;
    }

    .login-body {
      padding: 25px;
      color: #2d3748;
    }

    .login-body p {
      margin: 0 0 20px 0;
      font-weight: 500;
      color: #4a5568;
    }

    .user-list {
      display: flex;
      flex-direction: column;
      gap: 10px;
    }

    .user-option {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 15px;
      border: 2px solid #e2e8f0;
      border-radius: 12px;
      background: #f7fafc;
      cursor: pointer;
      transition: all 0.3s ease;
      font-weight: 500;
      color: #2d3748;
    }

    .user-option:hover {
      border-color: #4facfe;
      background: #ebf8ff;
      transform: translateY(-2px);
      box-shadow: 0 4px 15px rgba(79, 172, 254, 0.2);
    }

    .user-option i {
      font-size: 18px;
      color: #4facfe;
      min-width: 20px;
    }

    .user-option span {
      flex: 1;
      text-align: left;
    }

    #chat {
      height: 650px;
      overflow-y: auto;
      padding: 25px;
      background: #f8fafc;
      position: relative;
    }

    #chat::-webkit-scrollbar {
      width: 8px;
    }

    #chat::-webkit-scrollbar-track {
      background: #e2e8f0;
      border-radius: 4px;
    }

    #chat::-webkit-scrollbar-thumb {
      background: #cbd5e0;
      border-radius: 4px;
    }

    #chat::-webkit-scrollbar-thumb:hover {
      background: #a0aec0;
    }

    .message {
      margin: 15px 0;
      padding: 18px 22px;
      border-radius: 18px;
      max-width: 75%;
      min-width: 120px;
      width: fit-content;
      word-wrap: break-word;
      animation: messageSlide 0.3s ease-out;
      position: relative;
    }

    @keyframes messageSlide {
      from {
        opacity: 0;
        transform: translateY(10px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .user-message {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      margin-left: auto;
      margin-right: 0;
      box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
      position: relative;
    }

    .user-message::before {
      content: '';
      position: absolute;
      right: -8px;
      bottom: 10px;
      width: 0;
      height: 0;
      border-left: 8px solid #764ba2;
      border-top: 8px solid transparent;
    }

    .ai-message {
      background: white;
      color: #2d3748;
      margin-left: 0;
      margin-right: auto;
      border: 1px solid #e2e8f0;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
      white-space: pre-wrap;
      position: relative;
    }

    .ai-message::before {
      content: '';
      position: absolute;
      left: -8px;
      bottom: 10px;
      width: 0;
      height: 0;
      border-right: 8px solid white;
      border-top: 8px solid transparent;
    }

    .code-block {
      background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
      color: #e2e8f0;
      padding: 20px;
      border-radius: 12px;
      overflow-x: auto;
      font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
      font-size: 14px;
      line-height: 1.5;
      margin: 15px 0;
      border-left: 4px solid #4facfe;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    }

    .execution-result {
      background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
      color: white;
      padding: 18px;
      border-radius: 12px;
      margin-top: 10px;
      border-left: 4px solid #2d7d32;
      box-shadow: 0 4px 15px rgba(72, 187, 120, 0.3);
    }

    .input-container {
      padding: 25px 30px;
      background: white;
      border-top: 1px solid #e2e8f0;
      display: flex;
      gap: 12px;
      align-items: center;
    }

    .input-wrapper {
      position: relative;
      flex: 1;
    }

    #userInput {
      width: 100%;
      padding: 15px 20px;
      border: 2px solid #e2e8f0;
      border-radius: 25px;
      font-size: 16px;
      outline: none;
      transition: all 0.3s ease;
      background: #f7fafc;
    }

    #userInput:focus {
      border-color: #4facfe;
      background: white;
      box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
    }

    .btn {
      padding: 15px 20px;
      border: none;
      border-radius: 20px;
      cursor: pointer;
      font-size: 16px;
      font-weight: 600;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      gap: 8px;
      position: relative;
      overflow: hidden;
    }

    .btn::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 0;
      height: 0;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 50%;
      transition: all 0.6s ease;
      transform: translate(-50%, -50%);
    }

    .btn:hover::before {
      width: 300px;
      height: 300px;
    }

    .btn:active {
      transform: scale(0.98);
    }

    #uploadButton {
      background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
      color: white;
      box-shadow: 0 4px 15px rgba(72, 187, 120, 0.4);
    }

    #uploadButton:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(72, 187, 120, 0.5);
    }

    #newChatButton {
      background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
      color: white;
      box-shadow: 0 4px 15px rgba(237, 137, 54, 0.4);
    }

    #newChatButton:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(237, 137, 54, 0.5);
    }

    #sendButton {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
    }

    #sendButton:hover:not(:disabled) {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(102, 126, 234, 0.5);
    }

    #sendButton:disabled {
      background: #cbd5e0;
      color: #a0aec0;
      cursor: not-allowed;
      box-shadow: none;
    }

    .file-indicator {
      background: #4facfe;
      color: white;
      padding: 8px 15px;
      border-radius: 15px;
      font-size: 12px;
      margin-right: 10px;
      animation: pulse 2s infinite;
    }

    @keyframes pulse {
      0%, 100% { opacity: 1; }
      50% { opacity: 0.7; }
    }

    .typing-indicator {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 20px;
      background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%);
      border-left: 4px solid #4facfe;
      position: relative;
      overflow: hidden;
    }

    .typing-indicator::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(79, 172, 254, 0.1), transparent);
      animation: shimmer-loading 2s infinite;
    }

    @keyframes shimmer-loading {
      0% { left: -100%; }
      100% { left: 100%; }
    }

    .typing-text {
      color: #4facfe;
      font-weight: 500;
      margin-right: 8px;
      position: relative;
      z-index: 1;
    }

    .typing-dots {
      display: flex;
      gap: 4px;
      position: relative;
      z-index: 1;
    }

    .typing-dot {
      width: 8px;
      height: 8px;
      background: #4facfe;
      border-radius: 50%;
      animation: typing 1.4s infinite;
    }

    .typing-dot:nth-child(1) { animation-delay: 0s; }
    .typing-dot:nth-child(2) { animation-delay: 0.2s; }
    .typing-dot:nth-child(3) { animation-delay: 0.4s; }

    @keyframes typing {
      0%, 60%, 100% { 
        transform: translateY(0);
        opacity: 0.4;
      }
      30% { 
        transform: translateY(-8px);
        opacity: 1;
      }
    }

    .loading-spinner {
      display: inline-block;
      width: 16px;
      height: 16px;
      border: 2px solid rgba(79, 172, 254, 0.3);
      border-radius: 50%;
      border-top-color: #4facfe;
      animation: spin 1s ease-in-out infinite;
      position: relative;
      z-index: 1;
    }

    @keyframes spin {
      to { transform: rotate(360deg); }
    }

    /* AI消息等待动画样式 */
    .ai-waiting {
      display: flex;
      align-items: center;
      gap: 12px;
      opacity: 0.8;
    }

    .waiting-dots {
      display: flex;
      gap: 4px;
    }

    .waiting-dot {
      width: 8px;
      height: 8px;
      background: #4facfe;
      border-radius: 50%;
      animation: waitingBounce 1.4s infinite ease-in-out;
    }

    .waiting-dot:nth-child(1) { 
      animation-delay: -0.32s; 
    }

    .waiting-dot:nth-child(2) { 
      animation-delay: -0.16s; 
    }

    .waiting-dot:nth-child(3) { 
      animation-delay: 0s; 
    }

    @keyframes waitingBounce {
      0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
      }
      40% {
        transform: scale(1.1);
        opacity: 1;
      }
    }

    .waiting-text {
      color: #4facfe;
      font-size: 14px;
      font-weight: 500;
      animation: waitingPulse 2s infinite ease-in-out;
    }

    @keyframes waitingPulse {
      0%, 100% { 
        opacity: 0.6; 
      }
      50% { 
        opacity: 1; 
      }
    }

    .sidebar-toggle {
      display: none;
      position: fixed;
      top: 20px;
      left: 20px;
      z-index: 1000;
      background: rgba(45, 55, 72, 0.9);
      color: white;
      border: none;
      border-radius: 50%;
      width: 50px;
      height: 50px;
      cursor: pointer;
      font-size: 18px;
      backdrop-filter: blur(10px);
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    }

    @media (max-width: 768px) {
      body {
        padding: 10px;
      }
      
      .container {
        border-radius: 15px;
        flex-direction: column;
        position: relative;
      }
      
      .sidebar {
        position: fixed;
        top: 0;
        left: -320px;
        height: 100vh;
        width: 280px;
        z-index: 999;
        transition: left 0.3s ease;
        border-radius: 0 15px 15px 0;
      }
      
      .sidebar.show {
        left: 0;
      }

      .sidebar.collapsed {
        width: 280px;
      }

      .sidebar.collapsed .sidebar-title span,
      .sidebar.collapsed .search-box,
      .sidebar.collapsed .chat-item-title,
      .sidebar.collapsed .chat-item-preview,
      .sidebar.collapsed .chat-item-time {
        display: block;
      }

      .sidebar.collapsed .chat-item::before {
        display: none;
      }

      .collapse-button {
        display: none;
      }
      
      .sidebar-toggle {
        display: block;
      }
      
      .main-area {
        width: 100%;
      }
      
      .header {
        padding: 20px;
        border-radius: 15px 15px 0 0;
      }
      
      .header h1 {
        font-size: 1.8em;
      }
      
      #chat {
        height: 400px;
        padding: 20px;
      }
      
      .message {
        max-width: 85%;
        min-width: 100px;
        width: fit-content;
        padding: 15px 18px;
      }
      
      .input-container {
        padding: 20px;
        flex-wrap: wrap;
      }
      
      .btn {
        padding: 12px 16px;
        font-size: 14px;
      }
      
      .overlay {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 998;
      }
      
      .overlay.show {
        display: block;
      }

      /* 移动端用户信息样式 */
      .user-info {
        position: static;
        margin-top: 15px;
        justify-content: center;
      }

      .login-modal-content {
        width: 95%;
        max-width: 350px;
      }

      .login-body {
        padding: 20px;
      }
    }
  </style>
</head>
<body>
  <button class="sidebar-toggle" onclick="toggleSidebar()">
    <i class="fas fa-bars"></i>
  </button>
  
  <div class="overlay" onclick="closeSidebar()"></div>
  
  <div class="container">
    <div class="sidebar">
      <div class="sidebar-header">
        <div class="sidebar-title">
          <div style="display: flex; align-items: center; gap: 10px;">
            <i class="fas fa-comments"></i>
            <span>历史对话</span>
          </div>
          <button class="collapse-button" onclick="toggleSidebarCollapse()" title="折叠/展开">
            <i class="fas fa-angle-left" id="collapseIcon"></i>
          </button>
        </div>
        <input type="text" class="search-box" placeholder="搜索对话..." id="searchBox" oninput="searchChats()">
      </div>
      
      <div class="chat-list" id="chatList">
        <!-- 历史对话将在这里动态加载 -->
      </div>
    </div>
    
    <div class="main-area">
      <div class="header">
        <h1><i class="fas fa-robot"></i>迈维AI智能助手</h1>
        <div class="subtitle">您的专属人工智能伙伴</div>
        
        <!-- 用户信息区域 -->
        <div class="user-info">
          <div class="user-display" id="userDisplay" style="display: none;">
            <i class="fas fa-user-circle"></i>
            <span id="currentUser">未登录</span>
            <button class="logout-btn" onclick="logout()" title="注销">
              <i class="fas fa-sign-out-alt"></i>
            </button>
          </div>
          <button class="login-btn" id="loginButton" onclick="showLoginModal()">
            <i class="fas fa-sign-in-alt"></i> 登录
          </button>
        </div>
      </div>
      
      <div id="chat"></div>
      
      <div class="input-container">
        <input type="file" id="fileInput" multiple style="display: none;" />
        
        <button id="uploadButton" class="btn">
          <i class="fas fa-paperclip"></i>
        </button>
        
        <button id="newChatButton" class="btn" onclick="startNewConversation()">
          <i class="fas fa-plus"></i>
          新对话
        </button>
        
        <div class="input-wrapper">
          <input type="text" id="userInput" placeholder="输入您的问题..." />
        </div>
        
        <button id="sendButton" class="btn">
          <i class="fas fa-paper-plane"></i>
          发送
        </button>
      </div>
    </div>
  </div>

  <!-- 登录模态框 -->
  <div class="login-modal" id="loginModal">
    <div class="login-modal-content">
      <div class="login-header">
        <h3><i class="fas fa-user-circle"></i> 选择用户</h3>
        <button class="close-btn" onclick="hideLoginModal()">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="login-body">
        <p>选择一个测试用户登录：</p>
        <div class="user-list">
          <button class="user-option" onclick="login('admin')">
            <i class="fas fa-user-cog"></i>
            <span>用户1 (user1)</span>
          </button>
          <button class="user-option" onclick="login('user1')">
            <i class="fas fa-user"></i>
            <span>用户2 (user2)</span>
          </button>
          <button class="user-option" onclick="login('user2')">
            <i class="fas fa-user"></i>
            <span>用户3 (user3)</span>
          </button>
          <button class="user-option" onclick="login('guest')">
            <i class="fas fa-user-friends"></i>
            <span>用户4 (user4)</span>
          </button>
        </div>
      </div>
    </div>
  </div>

  <script>
    const chatDiv = document.getElementById('chat');
    const userInput = document.getElementById('userInput');
    const sendButton = document.getElementById('sendButton');
    let chatHistory = [];
    let chatSessions = {};
    let currentChatId = null;

    function formatMessage(text) {
      const codeBlockRegex = /```python([\s\S]*?)```/g;
      let formattedText = text.replace(codeBlockRegex, (match, code) => {
        return `<div class="code-block">${code}</div>`;
      });

      if (formattedText.includes("执行结果：")) {
        const parts = formattedText.split("执行结果：");
        const imageRegex = /\[([^\]]*)\]\(([^)]*)\)/g;
        let resultText = parts[1].replace(imageRegex, (match, alt, url) => {
          return `<img src="${url}" alt="${alt}" style="max-width: 100%; margin: 10px 0; border-radius: 8px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">`;
        });
        formattedText = parts[0] + `<div class="execution-result"><i class="fas fa-check-circle"></i> 执行结果：${resultText}</div>`;
      }

      return formattedText;
    }

    function generateUserId() {
      return ([1e7]+-1e3+-4e3+-8e3+-1e11).replace(/[018]/g, c =>
        (c ^ crypto.getRandomValues(new Uint8Array(1))[0] & 15 >> c / 4).toString(16)
      );
    }

    // 用户管理功能
    let currentUser = localStorage.getItem('currentUser') || null;
    let userId = currentUser || null;

    // 用户名映射
    const userNames = {
      'admin': '用户1',
      'user1': '用户2', 
      'user2': '用户3',
      'guest': '用户4'
    };

    // 显示/隐藏登录模态框
    function showLoginModal() {
      document.getElementById('loginModal').style.display = 'block';
    }

    function hideLoginModal() {
      document.getElementById('loginModal').style.display = 'none';
    }

    // 用户登录
    function login(username) {
      currentUser = username;
      userId = username;
      localStorage.setItem('currentUser', username);
      localStorage.setItem('userId', username);
      
      updateUserDisplay();
      hideLoginModal();
      
      // 启用输入控件
      checkLoginStatus();
      
      // 登录后重新加载对话列表和开始新对话
      loadChatList();
      startNewConversation();
      
      console.log('用户登录:', username);
    }

    // 用户注销
    function logout() {
      if (confirm('确定要注销登录吗？注销后将清空当前对话。')) {
        currentUser = null;
        userId = null;
        localStorage.removeItem('currentUser');
        localStorage.removeItem('userId');
        localStorage.removeItem('currentMessageId');
        
        updateUserDisplay();
        
        // 清空对话
        chatHistory = [];
        chatSessions = {};
        currentChatId = null;
        currentMessageId = null;
        document.getElementById('chat').innerHTML = '';
        document.getElementById('chatList').innerHTML = '<div style="color: rgba(255,255,255,0.7); padding: 20px; text-align: center;">请先登录</div>';
        
        console.log('用户已注销');
      }
    }

    // 更新用户显示
    function updateUserDisplay() {
      const userDisplay = document.getElementById('userDisplay');
      const loginButton = document.getElementById('loginButton');
      const currentUserSpan = document.getElementById('currentUser');
      
      if (currentUser) {
        userDisplay.style.display = 'flex';
        loginButton.style.display = 'none';
        currentUserSpan.textContent = userNames[currentUser] || currentUser;
      } else {
        userDisplay.style.display = 'none';
        loginButton.style.display = 'flex';
      }
    }

    // 检查用户登录状态
    function checkLoginStatus() {
      if (!currentUser) {
        // 未登录时显示提示
        const chatDiv = document.getElementById('chat');
        chatDiv.innerHTML = `
          <div style="text-align: center; padding: 50px; color: #718096;">
            <i class="fas fa-user-lock" style="font-size: 48px; margin-bottom: 20px; color: #4facfe;"></i>
            <h3 style="margin-bottom: 10px; color: #2d3748;">请先登录</h3>
            <p style="margin-bottom: 20px;">登录后即可开始与AI助手对话</p>
            <button onclick="showLoginModal()" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); color: white; border: none; padding: 12px 24px; border-radius: 20px; cursor: pointer; font-weight: 600;">
              <i class="fas fa-sign-in-alt"></i> 立即登录
            </button>
          </div>
        `;
        
        // 禁用输入和发送按钮
        document.getElementById('userInput').disabled = true;
        document.getElementById('sendButton').disabled = true;
        document.getElementById('uploadButton').disabled = true;
        document.getElementById('newChatButton').disabled = true;
        
        return false;
      } else {
        // 启用输入和发送按钮
        document.getElementById('userInput').disabled = false;
        document.getElementById('sendButton').disabled = false;
        document.getElementById('uploadButton').disabled = false;
        document.getElementById('newChatButton').disabled = false;
        
        return true;
      }
    }

    function generateMessageId() {
        return 'msg_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
    
    let currentMessageId = localStorage.getItem('currentMessageId') || generateMessageId();
    localStorage.setItem('currentMessageId', currentMessageId);
    
    function startNewConversation() {
        currentMessageId = generateMessageId();
        localStorage.setItem('currentMessageId', currentMessageId);
        chatHistory = [];
        document.getElementById('chat').innerHTML = '';
        console.log('开始新对话，ID:', currentMessageId);
        
        // 添加欢迎消息
        const welcomeDiv = document.createElement('div');
        welcomeDiv.className = 'message ai-message';
        welcomeDiv.innerHTML = '<i class="fas fa-sparkles"></i> 您好！我是您的AI助手，有什么可以帮您的吗？';
        chatDiv.appendChild(welcomeDiv);
    }

    function showTypingIndicator() {
      const typingDiv = document.createElement('div');
      typingDiv.className = 'message ai-message typing-indicator';
      typingDiv.innerHTML = `
        <i class="fas fa-robot"></i>
        <span class="typing-text">AI正在思考</span>
        <div class="loading-spinner"></div>
        <div class="typing-dots">
          <div class="typing-dot"></div>
          <div class="typing-dot"></div>
          <div class="typing-dot"></div>
        </div>
      `;
      typingDiv.id = 'typing-indicator';
      chatDiv.appendChild(typingDiv);
      chatDiv.scrollTop = chatDiv.scrollHeight;
      return typingDiv;
    }

    async function sendMessage() {
      const message = userInput.value;
      const fileInput = document.getElementById('fileInput');

      // 检查登录状态
      if (!checkLoginStatus()) {
        return;
      }

      if (!message.trim()) {
        userInput.style.borderColor = '#e53e3e';
        setTimeout(() => {
          userInput.style.borderColor = '#e2e8f0';
        }, 1000);
        return;
      }

      sendButton.disabled = true;
      userInput.disabled = true;
      sendButton.innerHTML = '<div class="loading-spinner" style="width: 14px; height: 14px; margin-right: 5px;"></div> 发送中...';

      const userDiv = document.createElement('div');
      userDiv.className = 'message user-message';
      userDiv.innerHTML = `<i class="fas fa-user"></i> ${message}`;
      chatDiv.appendChild(userDiv);
      userInput.value = '';

      const typingIndicator = showTypingIndicator();

                      try {
          // 确保有对话ID
          if (!currentChatId) {
            currentChatId = createNewChatId();
            currentMessageId = currentChatId;
            localStorage.setItem('currentMessageId', currentChatId);
          }
          
          chatHistory.push({ role: "user", content: message });

          const formData = new FormData();
          const chatMessage = {
            message: message,
            history: chatHistory.map(msg => ({ role: msg.role, content: msg.content }))
          };

          formData.append('chat_data', JSON.stringify(chatMessage));
          formData.append('user_id', userId);
          formData.append('message_id', currentChatId);

        if (fileInput.files.length > 0) {
          for (let i = 0; i < fileInput.files.length; i++) {
            formData.append('files', fileInput.files[i]);
          }
        }

        const response = await fetch('/chat', {
          method: 'POST',
          body: formData
        });

        typingIndicator.remove();

        const reader = response.body.getReader();
        const decoder = new TextDecoder('utf-8');
        let aiResponse = document.createElement('div');
        aiResponse.className = 'message ai-message';
        
        // 添加等待动画
        aiResponse.innerHTML = `
          <i class="fas fa-robot"></i> 
          <div class="ai-waiting">
            <div class="waiting-dots">
              <div class="waiting-dot"></div>
              <div class="waiting-dot"></div>
              <div class="waiting-dot"></div>
            </div>
            <span class="waiting-text">正在思考中...</span>
          </div>
        `;
        
        chatDiv.appendChild(aiResponse);
        chatDiv.scrollTop = chatDiv.scrollHeight;
        
        let fullResponse = '';
        let isFirstChunk = true;

        while (true) {
          const { done, value } = await reader.read();
          if (done) break;
          const text = decoder.decode(value, { stream: true });
          fullResponse += text;
          
          // 第一次接收到数据时，移除等待动画
          if (isFirstChunk) {
            aiResponse.innerHTML = '<i class="fas fa-robot"></i> ' + formatMessage(fullResponse);
            isFirstChunk = false;
          } else {
            aiResponse.innerHTML = '<i class="fas fa-robot"></i> ' + formatMessage(fullResponse);
          }
          
          chatDiv.scrollTop = chatDiv.scrollHeight;
        }

                  chatHistory.push({ role: "assistant", content: fullResponse });
          updateChatListAfterMessage();

      } catch (error) {
        if (typingIndicator.parentNode) {
          typingIndicator.remove();
        }
        const errorDiv = document.createElement('div');
        errorDiv.className = 'message ai-message';
        errorDiv.style.background = 'linear-gradient(135deg, #e53e3e 0%, #c53030 100%)';
        errorDiv.style.color = 'white';
        errorDiv.innerHTML = `<i class="fas fa-exclamation-triangle"></i> 抱歉，出现了错误: ${error.message}`;
        chatDiv.appendChild(errorDiv);
      } finally {
        sendButton.disabled = false;
        userInput.disabled = false;
        sendButton.innerHTML = '<i class="fas fa-paper-plane"></i> 发送';
        userInput.focus();
      }
    }

    sendButton.onclick = sendMessage;

    userInput.addEventListener('keypress', (e) => {
      if (e.key === 'Enter' && !userInput.disabled) {
        e.preventDefault();
        sendMessage();
      }
    });

    document.getElementById('uploadButton').onclick = () => {
      document.getElementById('fileInput').click();
    };

    document.getElementById('fileInput').onchange = function () {
      if (this.files.length > 0) {
        const names = Array.from(this.files).map(f => f.name).join(", ");
        const indicator = document.createElement('div');
        indicator.className = 'file-indicator';
        indicator.innerHTML = `<i class="fas fa-file"></i> 已选择: ${names}`;
        userInput.style.paddingLeft = '150px';
        userInput.parentNode.insertBefore(indicator, userInput);
      } else {
        userInput.style.paddingLeft = '20px';
        const indicator = userInput.parentNode.querySelector('.file-indicator');
        if (indicator) indicator.remove();
      }
    };

    document.getElementById('uploadButton').ondblclick = () => {
      const fileInput = document.getElementById('fileInput');
      fileInput.value = '';
      userInput.style.paddingLeft = '20px';
      const indicator = userInput.parentNode.querySelector('.file-indicator');
      if (indicator) indicator.remove();
    };

    // 侧边栏功能
    function toggleSidebar() {
      const sidebar = document.querySelector('.sidebar');
      const overlay = document.querySelector('.overlay');
      sidebar.classList.toggle('show');
      overlay.classList.toggle('show');
    }

    function closeSidebar() {
      const sidebar = document.querySelector('.sidebar');
      const overlay = document.querySelector('.overlay');
      sidebar.classList.remove('show');
      overlay.classList.remove('show');
    }

    // 折叠侧边栏功能
    function toggleSidebarCollapse() {
      const sidebar = document.querySelector('.sidebar');
      const collapseIcon = document.getElementById('collapseIcon');
      const isCollapsed = sidebar.classList.contains('collapsed');
      
      if (isCollapsed) {
        sidebar.classList.remove('collapsed');
        collapseIcon.className = 'fas fa-angle-left';
      } else {
        sidebar.classList.add('collapsed');
        collapseIcon.className = 'fas fa-angle-right';
      }
      
      // 保存折叠状态
      localStorage.setItem('sidebarCollapsed', !isCollapsed);
    }

    // 恢复侧边栏折叠状态
    function restoreSidebarState() {
      const isCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
      if (isCollapsed) {
        const sidebar = document.querySelector('.sidebar');
        const collapseIcon = document.getElementById('collapseIcon');
        sidebar.classList.add('collapsed');
        collapseIcon.className = 'fas fa-angle-right';
      }
    }

    // 从服务器加载历史对话列表
    async function loadChatList() {
      const chatList = document.getElementById('chatList');
      
      try {
        const response = await fetch(`/conversations/${userId}`);
        const data = await response.json();
        
        if (data.success) {
          chatSessions = {};
          data.conversations.forEach(conv => {
            chatSessions[conv.id] = {
              id: conv.id,
              title: conv.title,
              lastMessage: conv.lastMessage,
              lastUpdate: conv.lastUpdate,
              createdAt: conv.createdAt,
              messageCount: conv.messageCount
            };
          });
          
          renderChatList();
        } else {
          console.error('加载对话列表失败:', data.error);
          chatList.innerHTML = '<div style="color: rgba(255,255,255,0.7); padding: 20px; text-align: center;">加载对话失败</div>';
        }
      } catch (error) {
        console.error('请求对话列表失败:', error);
        chatList.innerHTML = '<div style="color: rgba(255,255,255,0.7); padding: 20px; text-align: center;">网络错误</div>';
      }
    }

    // 渲染对话列表UI
    function renderChatList() {
      const chatList = document.getElementById('chatList');
      chatList.innerHTML = '';
      
      const sessions = Object.entries(chatSessions).sort((a, b) => 
        new Date(b[1].lastUpdate) - new Date(a[1].lastUpdate)
      );
      
      if (sessions.length === 0) {
        chatList.innerHTML = '<div style="color: rgba(255,255,255,0.7); padding: 20px; text-align: center;">暂无对话历史</div>';
        return;
      }
      
      sessions.forEach(([sessionId, session]) => {
        const chatItem = document.createElement('div');
        chatItem.className = 'chat-item';
        if (sessionId === currentChatId) {
          chatItem.classList.add('active');
        }
        
        const title = session.title || '新对话';
        const preview = session.lastMessage || '暂无消息';
        const time = formatTime(session.lastUpdate);
        
        // 为折叠状态添加工具提示
        chatItem.setAttribute('data-title', title);
        
        chatItem.innerHTML = `
          <div class="chat-item-title">
            <span>${title}</span>
            <div>
              <span class="chat-item-time">${time}</span>
              <i class="fas fa-trash chat-item-delete" onclick="deleteChat('${sessionId}')" title="删除对话"></i>
            </div>
          </div>
          <div class="chat-item-preview">${preview}</div>
        `;
        
        chatItem.onclick = (e) => {
          if (!e.target.classList.contains('chat-item-delete')) {
            loadChat(sessionId);
            closeSidebar();
          }
        };
        
        chatList.appendChild(chatItem);
      });
    }

    // 格式化时间显示
    function formatTime(timestamp) {
      const date = new Date(timestamp);
      const now = new Date();
      const diff = now - date;
      
      if (diff < 60000) return '刚刚';
      if (diff < 3600000) return Math.floor(diff / 60000) + '分钟前';
      if (diff < 86400000) return Math.floor(diff / 3600000) + '小时前';
      if (diff < 604800000) return Math.floor(diff / 86400000) + '天前';
      
      return date.toLocaleDateString();
    }

    // 创建新对话ID
    function createNewChatId() {
      return 'chat_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    // 加载指定对话
    async function loadChat(sessionId) {
      if (!chatSessions[sessionId]) return;
      
      currentChatId = sessionId;
      currentMessageId = sessionId; // 保持兼容性
      localStorage.setItem('currentMessageId', sessionId);
      
      try {
        // 从服务器加载消息
        const response = await fetch(`/conversations/${userId}/${sessionId}/messages`);
        const data = await response.json();
        
        if (data.success) {
          chatHistory = [];
          
          // 清空并重新渲染聊天界面
          chatDiv.innerHTML = '';
          
          data.messages.forEach(msg => {
            // 更新本地聊天历史
            chatHistory.push({ role: msg.role === 'human' ? 'user' : msg.role, content: msg.content });
            
            const messageDiv = document.createElement('div');
            if (msg.role === 'human') {
              messageDiv.className = 'message user-message';
              messageDiv.innerHTML = `<i class="fas fa-user"></i> ${msg.content}`;
            } else if (msg.role === 'ai') {
              messageDiv.className = 'message ai-message';
              
              // 处理AI消息，检查是否包含图像路径
              let messageContent = msg.content;
              if (msg.image_path) {
                // 将图像路径转换为相对URL
                const relativePath = msg.image_path.replace(/\\/g, '/').replace(/^static\//, '');
                const imageUrl = `/static/${relativePath}`;
                
                // 如果消息内容中不包含图像链接，则添加图像信息
                if (!messageContent.includes('[生成的图像]') && !messageContent.includes('<img')) {
                  messageContent += `\n执行结果：\n执行成功\n[生成的图像](${imageUrl})`;
                }
              }
              
              messageDiv.innerHTML = `<i class="fas fa-robot"></i> ${formatMessage(messageContent)}`;
            }
            // 跳过system消息，不显示在界面上
            
            if (msg.role !== 'system') {
              chatDiv.appendChild(messageDiv);
            }
          });
          
          chatDiv.scrollTop = chatDiv.scrollHeight;
          renderChatList(); // 更新侧边栏高亮
        } else {
          console.error('加载对话消息失败:', data.error);
          alert('加载对话失败: ' + data.error);
        }
      } catch (error) {
        console.error('请求对话消息失败:', error);
        alert('加载对话失败，请检查网络连接');
      }
    }

    // 删除对话
    async function deleteChat(sessionId) {
      if (confirm('确定要删除这个对话吗？')) {
        try {
          const response = await fetch(`/conversations/${userId}/${sessionId}`, {
            method: 'DELETE'
          });
          
          const data = await response.json();
          
          if (data.success) {
            // 从本地缓存中删除
            delete chatSessions[sessionId];
            
            if (currentChatId === sessionId) {
              // 如果删除的是当前对话，开始新对话
              startNewConversation();
            } else {
              // 重新渲染对话列表
              renderChatList();
            }
          } else {
            alert('删除失败: ' + data.error);
          }
        } catch (error) {
          console.error('删除对话失败:', error);
          alert('删除失败，请检查网络连接');
        }
      }
    }

    // 搜索对话
    function searchChats() {
      const searchTerm = document.getElementById('searchBox').value.toLowerCase();
      const chatItems = document.querySelectorAll('.chat-item');
      
      chatItems.forEach(item => {
        const title = item.querySelector('.chat-item-title span').textContent.toLowerCase();
        const preview = item.querySelector('.chat-item-preview').textContent.toLowerCase();
        
        if (title.includes(searchTerm) || preview.includes(searchTerm)) {
          item.style.display = 'block';
        } else {
          item.style.display = 'none';
        }
      });
    }

    // 在消息发送后更新对话列表
    function updateChatListAfterMessage() {
      // 重新加载对话列表以获取最新状态
      loadChatList();
    }

    // 开始新对话
    function startNewConversation() {
      currentChatId = createNewChatId();
      currentMessageId = currentChatId;
      localStorage.setItem('currentMessageId', currentChatId);
      
      chatHistory = [];
      chatDiv.innerHTML = '';
      
      // 添加欢迎消息
      const welcomeDiv = document.createElement('div');
      welcomeDiv.className = 'message ai-message';
      welcomeDiv.innerHTML = '<i class="fas fa-sparkles"></i> 您好！我是您的AI助手，有什么可以帮您的吗？';
      chatDiv.appendChild(welcomeDiv);
      
      // 更新侧边栏显示
      renderChatList();
      console.log('开始新对话，ID:', currentChatId);
    }

    // 点击模态框外部关闭模态框
    document.getElementById('loginModal').addEventListener('click', function(e) {
      if (e.target === this) {
        hideLoginModal();
      }
    });

    // 初始化
    window.addEventListener('load', async () => {
      // 恢复侧边栏折叠状态
      restoreSidebarState();
      
      // 更新用户显示
      updateUserDisplay();
      
      // 检查登录状态
      if (!checkLoginStatus()) {
        return;
      }
      
      // 先加载对话列表
      await loadChatList();
      
      // 检查是否有当前对话ID
      const savedMessageId = localStorage.getItem('currentMessageId');
      if (savedMessageId && chatSessions[savedMessageId]) {
        loadChat(savedMessageId);
      } else {
        // 如果有历史对话，加载最新的一个
        const sessionIds = Object.keys(chatSessions);
        if (sessionIds.length > 0) {
          const latestSession = Object.values(chatSessions).sort((a, b) => 
            new Date(b.lastUpdate) - new Date(a.lastUpdate)
          )[0];
          loadChat(latestSession.id);
        } else {
          // 没有历史对话，创建新对话
          startNewConversation();
        }
      }
    });
  </script>
</body>
</html>