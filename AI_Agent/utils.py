"""
工具函数模块 - 包含日志、时间、文件处理等通用工具函数
"""
import os
import uuid
import datetime
import hashlib
import pandas as pd
import pytz
from config import FILES_DIR, IMAGES_DIR

def red_log(text):
    """打印红色日志"""
    print(f"\033[91m\033[1m🔴 {text}\033[0m")

def get_beijing_time():
    """获取当前北京时间字符串"""
    beijing_tz = pytz.timezone('Asia/Shanghai')
    return datetime.datetime.now(beijing_tz).strftime('%Y-%m-%d %H:%M:%S')

def calculate_file_hash(file_data: bytes) -> str:
    """计算文件内容的SHA256哈希值"""
    return hashlib.sha256(file_data).hexdigest()

def get_file_cache_key(filename: str, file_data: bytes) -> str:
    """生成文件缓存key：原始文件名 + 内容哈希值"""
    file_hash = calculate_file_hash(file_data)
    return f"{filename}_{file_hash[:16]}"  # 使用前16位哈希值

def validate_dataframe(file_path: str) -> list:
    """验证数据文件的格式和内容"""
    issues = []
    ext = os.path.splitext(file_path)[1].lower()

    try:
        if ext in [".csv", ".txt", ".tsv"]:
            df = pd.read_csv(file_path, sep=None, engine="python", nrows=100)
        elif ext in [".xlsx", ".xls"]:
            df = pd.read_excel(file_path, header=0, nrows=100)
        else:
            return []  # 非结构化文件跳过

        # === 空表格判断 ===
        if df.empty:
            issues.append(" 表格为空。")

        # === 缺失值检查 ===
        if df.isnull().values.any():
            issues.append(" 存在缺失值（NaN），请将NAN替换成数值后再提交！")

        # === 检查列名合法性 ===
        invalid_cols = []
        for col in df.columns:
            if (
                pd.isnull(col) or
                not isinstance(col, str) or
                str(col).strip() == "" or
                str(col).lower().startswith("unnamed") or
                str(col).replace(".", "", 1).lstrip("-").isdigit()
            ):
                invalid_cols.append(str(col))

        if invalid_cols:
            issues.append(f" 存在疑似无效列名：{', '.join(invalid_cols)}")

        # === 检查第一行是否疑似被误当作列名 ===
        if df.shape[0] > 0:
            first_row = df.iloc[0].astype(str).tolist()
            col_names = df.columns.astype(str).tolist()
            overlap = sum(1 for c in col_names if c in first_row)
            if overlap >= len(col_names) * 0.5:
                issues.append("第一行数据可能被错误地识别为列名，请检查表头。")

    except Exception as e:
        issues.append(f" 数据读取失败：{str(e)}")

    return issues

def get_file_path(filename: str) -> str:
    """生成按日期组织的文件保存路径"""
    # 获取当前日期作为子目录
    today = datetime.datetime.now().strftime('%Y-%m-%d')
    # 构建保存路径：static/files/年-月-日/
    save_dir = os.path.join(FILES_DIR, today)
    
    # 确保目录存在
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)
    
    # 生成带时间戳的文件名，确保唯一性
    timestamp = datetime.datetime.now().strftime('%H-%M-%S')
    unique_id = str(uuid.uuid4())[:8]  # 使用UUID的前8位
    filename = f"file_{timestamp}_{unique_id}_{filename}"
    return os.path.join(save_dir, filename)

def get_image_path() -> str:
    """生成按日期组织的图片保存路径"""
    # 获取当前日期作为子目录
    today = datetime.datetime.now().strftime('%Y-%m-%d')
    # 构建保存路径：static/images/年-月-日/
    save_dir = os.path.join(IMAGES_DIR, today)
    
    # 确保目录存在
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)
    
    # 生成带时间戳的文件名，确保唯一性
    timestamp = datetime.datetime.now().strftime('%H-%M-%S')
    unique_id = str(uuid.uuid4())[:8]  # 使用UUID的前8位
    filename = f"plot_{timestamp}_{unique_id}.png"
    
    return os.path.join(save_dir, filename) 