import os
from dotenv import load_dotenv
from langchain_openai import ChatOpenAI
from AI.tools import PythonPlottingTool, PythonAnalysisTool
from langchain.agents import Tool

from utils import red_log

# 加载环境变量
load_dotenv()

red_log("初始化远程LLM模型 - deepseek-v3")

# 从环境变量获取API配置
api_key = os.getenv("BAILIAN_API_KEY")
base_url = os.getenv("BASE_URL")

if not api_key:
    raise ValueError("BAILIAN_API_KEY环境变量未设置，请检查.env文件")

model = ChatOpenAI(
    temperature = 1,
    # model="qwen-long",
    model="deepseek-v3",
    api_key=api_key,
    base_url=base_url,
    max_tokens = 2000,
)
image_model = ChatOpenAI(
    model="qwen-vl-max",
    #model="qvq-max",
    api_key=api_key,
    base_url=base_url,
    max_tokens = 2000,
)

# 创建专门的绘图工具实例
python_plotting_tool = PythonPlottingTool(
    security_llm=model,  # 传入 LLM 模型用于安全检查
    enable_security_check=True  # 启用安全检查
)

# 创建专门的数据分析工具实例
python_analysis_tool = PythonAnalysisTool(
    security_llm=model,  # 传入 LLM 模型用于安全检查
    enable_security_check=True  # 启用安全检查
)

# 绘图工具集（用于绘图Agent）
plotting_tools = [
    Tool(
        name="python绘图",
        func=python_plotting_tool.run,
        description=python_plotting_tool.description
    ),
]

# 数据分析工具集（用于分析Agent）
analysis_tools = [
    Tool(
        name="python数据分析",
        func=python_analysis_tool.run,
        description=python_analysis_tool.description
    ),
]

# 对话工具集（用于对话Agent，不包含Python工具）
chat_tools = []

# 注意：旧的Agent实现已移除，现在使用agent_manager.py中的多Agent架构

