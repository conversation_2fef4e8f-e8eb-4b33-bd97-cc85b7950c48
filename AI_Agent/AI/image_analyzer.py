"""
图片分析模块
使用多模态LLM分析图片内容，提取视觉风格特征
"""

import base64
import os
from typing import Optional


def analyze_image_with_llm(image_path: str) -> str:
    """
    使用多模态LLM分析图片内容
    
    Args:
        image_path: 图片文件路径
        
    Returns:
        str: 图片的详细描述内容，包括视觉风格、布局、颜色等信息
    """
    try:
        # 导入模型（避免循环导入）
        from .agent import image_model
        
        # 检查文件是否存在
        if not os.path.exists(image_path):
            return f"图片文件不存在：{image_path}"
        
        # 读取图片并转换为base64
        with open(image_path, "rb") as image_file:
            image_data = base64.b64encode(image_file.read()).decode('utf-8')
        
        # 构建多模态消息
        from langchain.schema import HumanMessage
        
        # 创建包含图片的消息
        message_content = [
            {
                "type": "text", 
                "text": (
                    "请详细描述这张生物信息学图形的内容，特别关注以下方面：\n"
                    "1. 图片的主要元素和对象\n"
                    "2. 颜色搭配和视觉风格（配色方案、渐变效果、色彩饱和度等）\n"
                    "3. 布局和构图特点（元素排列、对齐方式、留白等）\n"
                    "4. 如果是图表，请详细描述：\n"
                    "   - 图表类型（柱状图、折线图、饼图等）\n"
                    "   - 数据展示方式\n"
                    "   - 标题和标签样式\n"
                    "   - 坐标轴设计\n"
                    "   - 图例样式\n"
                    "   - 数值标注方式\n"
                    "\n请用中文回答，内容要详细具体，便于后续的图表风格模仿。"
                )
            },
            {
                "type": "image_url",
                "image_url": {
                    "url": f"data:image/jpeg;base64,{image_data}"
                }
            }
        ]
        
        # 调用多模态LLM
        print(f"🔍 正在使用多模态LLM分析图片: {os.path.basename(image_path)}")
        response = image_model.invoke([HumanMessage(content=message_content)])
        
        # 提取响应内容
        description = ""
        if hasattr(response, 'content'):
            description = response.content
        else:
            description = str(response)
        
        print(f"✅ 图片分析完成，描述长度: {len(description)} 字符")
        return description
            
    except Exception as e:
        error_msg = f"图片分析失败：{str(e)}"
        print(f"❌ {error_msg}")
        return error_msg


def is_image_file(filename: str) -> bool:
    """
    判断文件是否为支持的图片格式
    
    Args:
        filename: 文件名
        
    Returns:
        bool: 是否为图片文件
    """
    image_exts = [".png", ".jpg", ".jpeg", ".bmp", ".gif"]
    ext = os.path.splitext(filename)[1].lower()
    return ext in image_exts


def get_image_file_info(image_path: str) -> dict:
    """
    获取图片文件的基本信息
    
    Args:
        image_path: 图片文件路径
        
    Returns:
        dict: 包含文件大小、格式等信息的字典
    """
    try:
        import os
        from PIL import Image
        
        # 基本文件信息
        file_size = os.path.getsize(image_path)
        file_ext = os.path.splitext(image_path)[1].lower()
        
        # 图片尺寸信息
        with Image.open(image_path) as img:
            width, height = img.size
            mode = img.mode
        
        return {
            "file_size": file_size,
            "file_size_mb": round(file_size / (1024 * 1024), 2),
            "format": file_ext,
            "dimensions": f"{width}x{height}",
            "width": width,
            "height": height,
            "color_mode": mode
        }
        
    except Exception as e:
        return {"error": f"获取图片信息失败：{str(e)}"} 