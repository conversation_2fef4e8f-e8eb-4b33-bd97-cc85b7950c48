"""
智能意图分类器 - 支持上下文感知的意图分类和置信度评估
"""
import re
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from enum import Enum


class IntentType(Enum):
    """意图类型枚举"""
    GREETING = "GREETING"           # 问候、闲聊
    QUESTION = "QUESTION"           # 一般咨询  
    ANALYSIS = "ANALYSIS"           # 数据分析
    PLOTTING = "PLOTTING"           # 绘图需求
    MIXED = "MIXED"                 # 复合需求


@dataclass
class IntentResult:
    """意图分类结果"""
    intent: IntentType
    confidence: float               # 置信度 (0.0-1.0)
    reasoning: str                  # 分类依据
    sub_intents: Dict[str, float]   # 各子意图的得分


class AdvancedIntentClassifier:
    """高级意图分类器"""
    
    def __init__(self):
        """初始化分类器"""
        self._init_patterns()
        self._init_weights()
    
    def _init_patterns(self):
        """初始化关键词模式"""
        # 更精确的关键词组
        self.patterns = {
            'greeting': {
                'simple': ['你好', 'hello', 'hi', '您好', '嗨'],
                'polite': ['早上好', '下午好', '晚上好', '午安'],
                'farewell': ['再见', 'bye', '拜拜', '结束'],
                'thanks': ['谢谢', '感谢', 'thanks', 'thank you'],
                'positive': ['不错', '很好', '棒', '厉害', '赞']
            },
            'plotting': {
                'explicit': ['绘图', '画图', '图表', '可视化', '画', '绘制'],
                'chart_types': ['柱状图', '折线图', '散点图', '饼图', '热力图', '箱线图', 
                              '小提琴图', '直方图', 'barplot', 'lineplot', 'scatterplot', 
                              'heatmap', 'boxplot', 'violinplot', 'histogram'],
                'plot_libs': ['matplotlib', 'seaborn', 'plt', 'plotly'],
                'plot_actions': ['保存图', '生成图', '输出图', '创建图', 'save figure'],
                'visual_terms': ['颜色', '标题', '标签', '图例', '坐标轴', '样式', '字体', '背景'],
                'modifications': ['放置', '移动', '调整', '修改', '改变', '设置', '更新', '替换'],
                'positions': ['左侧', '右侧', '上方', '下方', '顶部', '底部', '中间', '居中'],
                'visual_props': ['大小', '粗细', '透明度', '旋转', '位置', '间距', '边距']
            },
            'analysis': {
                'explicit': ['分析', '统计', '计算', '处理'],
                'data_ops': ['清洗', '筛选', '过滤', '汇总', '合并', '分组', '排序'],
                'stats': ['平均', '最大', '最小', '方差', '标准差', '中位数', '百分位'],
                'advanced': ['相关', '回归', '聚类', '主成分', '降维', '模型'],
                'data_terms': ['数据', '表格', 'dataframe', 'csv', 'excel', '字段', '列']
            },
            'question': {
                'what': ['什么是', '什么叫', 'what is', 'what are'],
                'how': ['如何', '怎么', '怎样', 'how to', 'how can'],
                'why': ['为什么', '为啥', 'why', '原因'],
                'can': ['能否', '可以吗', '是否可以', 'can you', 'could you'],
                'explain': ['解释', '说明', '介绍', '讲解', 'explain', 'describe'],
                'compare': ['比较', '对比', '差异', 'compare', 'difference']
            }
        }
        
        # 编译正则表达式模式
        self.regex_patterns = {
            'file_reference': re.compile(r'文件|数据|csv|excel|表格|上传', re.IGNORECASE),
            'modification': re.compile(r'修改|调整|改变|优化|更新', re.IGNORECASE),
            'continuation': re.compile(r'继续|还有|另外|此外|然后', re.IGNORECASE),
            'negation': re.compile(r'不要|不需要|别|不用', re.IGNORECASE)
        }
    
    def _init_weights(self):
        """初始化权重配置"""
        self.weights = {
            'keyword_match': 0.6,      # 关键词匹配权重（提高）
            'context_coherence': 0.2,  # 上下文连贯性权重  
            'file_presence': 0.15,     # 文件存在权重
            'message_complexity': 0.03, # 消息复杂度权重（降低）
            'pattern_match': 0.02      # 正则模式权重
        }
        
        # 意图转换权重矩阵 (从历史意图到当前意图的转换概率)
        self.transition_weights = {
            IntentType.GREETING: {
                IntentType.QUESTION: 0.6,
                IntentType.ANALYSIS: 0.2,
                IntentType.PLOTTING: 0.2
            },
            IntentType.QUESTION: {
                IntentType.ANALYSIS: 0.4,
                IntentType.PLOTTING: 0.3,
                IntentType.QUESTION: 0.3
            },
            IntentType.ANALYSIS: {
                IntentType.PLOTTING: 0.5,
                IntentType.ANALYSIS: 0.3,
                IntentType.QUESTION: 0.2
            },
            IntentType.PLOTTING: {
                IntentType.PLOTTING: 0.4,
                IntentType.ANALYSIS: 0.3,
                IntentType.QUESTION: 0.3
            }
        }
    
    def classify(self, 
                user_input: str, 
                has_files: bool, 
                context_history: Optional[List[str]] = None,
                previous_intent: Optional[IntentType] = None) -> IntentResult:
        """
        执行意图分类
        
        Args:
            user_input: 用户输入
            has_files: 是否有文件上传
            context_history: 对话历史上下文 (最近几轮对话)
            previous_intent: 上一轮的意图类型
            
        Returns:
            IntentResult: 分类结果
        """
        # 计算各个特征得分
        keyword_scores = self._calculate_keyword_scores(user_input)
        context_scores = self._calculate_context_scores(user_input, context_history)
        file_scores = self._calculate_file_scores(has_files, user_input)
        complexity_scores = self._calculate_complexity_scores(user_input)
        pattern_scores = self._calculate_pattern_scores(user_input)
        
        # 计算综合得分
        total_scores = {}
        for intent in IntentType:
            score = (
                keyword_scores.get(intent, 0) * self.weights['keyword_match'] +
                context_scores.get(intent, 0) * self.weights['context_coherence'] +
                file_scores.get(intent, 0) * self.weights['file_presence'] +
                complexity_scores.get(intent, 0) * self.weights['message_complexity'] +
                pattern_scores.get(intent, 0) * self.weights['pattern_match']
            )
            
            # 应用历史意图的转换权重
            if previous_intent and previous_intent in self.transition_weights:
                transition_weight = self.transition_weights[previous_intent].get(intent, 0.1)
                score *= (1 + transition_weight * 0.2)  # 调节转换影响强度
            
            total_scores[intent] = max(0, min(1, score))  # 限制在 [0,1] 范围
        
        # 选择最高得分的意图
        best_intent = max(total_scores, key=total_scores.get)
        confidence = total_scores[best_intent]
        
        # 检查是否为复合意图
        if self._is_mixed_intent(total_scores, confidence):
            best_intent = IntentType.MIXED
            confidence = self._calculate_mixed_confidence(total_scores)
        
        # 生成推理说明
        reasoning = self._generate_reasoning(
            best_intent, keyword_scores, context_scores, 
            file_scores, has_files, user_input
        )
        
        return IntentResult(
            intent=best_intent,
            confidence=confidence,
            reasoning=reasoning,
            sub_intents=total_scores
        )
    
    def _calculate_keyword_scores(self, user_input: str) -> Dict[IntentType, float]:
        """计算关键词匹配得分"""
        input_lower = user_input.lower()
        scores = {}
        
        for intent_name, pattern_groups in self.patterns.items():
            total_score = 0
            group_weights = {
                # 为不同类型的关键词组设置权重
                'explicit': 1.0,
                'chart_types': 1.0,
                'visual_terms': 0.9,
                'modifications': 0.8,
                'positions': 0.8,
                'visual_props': 0.7,
                'plot_libs': 0.6,
                'plot_actions': 0.8,
                'simple': 1.0,
                'polite': 1.0,
                'farewell': 1.0,
                'thanks': 0.8,
                'positive': 0.6,
                'data_ops': 0.9,
                'stats': 0.8,
                'advanced': 0.9,
                'data_terms': 0.7,
                'what': 1.0,
                'how': 1.0,
                'why': 1.0,
                'can': 0.8,
                'explain': 0.9,
                'compare': 0.8
            }
            
            for group_name, keywords in pattern_groups.items():
                group_weight = group_weights.get(group_name, 0.5)
                matches = sum(1 for keyword in keywords if keyword in input_lower)
                if matches > 0:
                    # 有匹配就给满分，不按比例计算
                    group_score = group_weight * min(1.0, matches / max(1, len(keywords) * 0.3))
                    total_score += group_score
            
            # 标准化得分
            if total_score > 0:
                # 对绘图意图给予额外加成（因为绘图词汇较多样）
                if intent_name == 'plotting':
                    total_score *= 1.2
                    
                normalized_score = min(1.0, total_score / 3)  # 除以3进行标准化
                
                intent_type = {
                    'greeting': IntentType.GREETING,
                    'plotting': IntentType.PLOTTING, 
                    'analysis': IntentType.ANALYSIS,
                    'question': IntentType.QUESTION
                }.get(intent_name)
                
                if intent_type:
                    scores[intent_type] = normalized_score
        
        return scores
    
    def _calculate_context_scores(self, user_input: str, context_history: Optional[List[str]]) -> Dict[IntentType, float]:
        """计算上下文连贯性得分"""
        scores = {intent: 0.0 for intent in IntentType}
        
        if not context_history:
            return scores
        
        # 分析最近的对话内容
        recent_context = ' '.join(context_history[-3:]).lower()  # 最近3轮对话
        input_lower = user_input.lower()
        
        # 检查上下文连续性指标
        continuation_indicators = ['继续', '还有', '另外', '然后', '接下来', '再', '也']
        has_continuation = any(indicator in input_lower for indicator in continuation_indicators)
        
        modification_indicators = ['修改', '调整', '改变', '换个', '重新', '优化', '设置', '放置', '移动']
        has_modification = any(indicator in input_lower for indicator in modification_indicators)
        
        # 强化绘图上下文检测
        plotting_context_keywords = ['绘图', '图表', '画图', '柱状图', '折线图', '散点图', '饼图', '可视化', 'plot', 'chart']
        has_plotting_context = any(keyword in recent_context for keyword in plotting_context_keywords)
        
        # 检测当前消息是否包含图表修改相关词汇
        chart_modification_terms = [
            '图例', '标题', '颜色', '标签', '坐标轴', '样式', '字体', '背景',
            '左侧', '右侧', '上方', '下方', '顶部', '底部', '中间', '居中',
            '大小', '粗细', '透明度', '旋转', '位置', '间距', '边距'
        ]
        has_chart_terms = any(term in input_lower for term in chart_modification_terms)
        
        # 基于上下文内容推断意图连续性
        if has_plotting_context:
            if has_modification or has_chart_terms:
                scores[IntentType.PLOTTING] += 0.8  # 强烈倾向绘图
            elif has_continuation:
                scores[IntentType.PLOTTING] += 0.6
            else:
                scores[IntentType.PLOTTING] += 0.4
        
        # 即使没有绘图上下文，如果消息包含图表术语，也可能是绘图需求
        if has_chart_terms and not has_plotting_context:
            scores[IntentType.PLOTTING] += 0.5
        
        # 数据分析上下文
        analysis_context_keywords = ['分析', '数据', '统计', '计算', '处理']
        has_analysis_context = any(keyword in recent_context for keyword in analysis_context_keywords)
        
        if has_analysis_context:
            if has_continuation:
                scores[IntentType.ANALYSIS] += 0.5
            scores[IntentType.ANALYSIS] += 0.3
        
        return scores
    
    def _calculate_file_scores(self, has_files: bool, user_input: str) -> Dict[IntentType, float]:
        """计算文件相关得分"""
        scores = {intent: 0.0 for intent in IntentType}
        
        if has_files:
            # 有文件上传时的得分分布
            scores[IntentType.ANALYSIS] = 0.8
            scores[IntentType.PLOTTING] = 0.7
            scores[IntentType.QUESTION] = 0.2
            scores[IntentType.GREETING] = 0.1
        else:
            # 检查是否提及文件或数据
            if self.regex_patterns['file_reference'].search(user_input):
                scores[IntentType.ANALYSIS] = 0.6
                scores[IntentType.PLOTTING] = 0.5
        
        return scores
    
    def _calculate_complexity_scores(self, user_input: str) -> Dict[IntentType, float]:
        """计算消息复杂度得分（降低权重，不主导分类）"""
        scores = {intent: 0.0 for intent in IntentType}
        
        message_length = len(user_input)
        sentence_count = len([s for s in user_input.split('。') if s.strip()])
        word_count = len(user_input.split())
        
        # 复杂度指标（降低影响）
        complexity_score = min(1.0, (message_length / 200 + sentence_count / 5 + word_count / 50) / 3)
        
        # 不再基于长度强制分类，而是给出温和的倾向性得分
        if complexity_score < 0.15 and message_length < 10:
            # 极短消息（如"好的"、"嗯"等），轻微倾向问候
            scores[IntentType.GREETING] = 0.2
        elif complexity_score > 0.7:
            # 非常复杂的消息，轻微倾向混合需求
            scores[IntentType.MIXED] = 0.3
            scores[IntentType.ANALYSIS] = 0.2
        else:
            # 其他情况保持中性，让关键词匹配起主导作用
            for intent in IntentType:
                scores[intent] = 0.1
        
        return scores
    
    def _calculate_pattern_scores(self, user_input: str) -> Dict[IntentType, float]:
        """计算正则模式匹配得分"""
        scores = {intent: 0.0 for intent in IntentType}
        
        # 检查否定模式
        if self.regex_patterns['negation'].search(user_input):
            # 可能是拒绝某个操作或澄清需求
            scores[IntentType.QUESTION] += 0.3
        
        # 检查修改模式  
        if self.regex_patterns['modification'].search(user_input):
            scores[IntentType.PLOTTING] += 0.4
            scores[IntentType.ANALYSIS] += 0.3
        
        return scores
    
    def _is_mixed_intent(self, scores: Dict[IntentType, float], best_confidence: float) -> bool:
        """判断是否为复合意图"""
        if best_confidence < 0.6:  # 最高置信度不够高
            # 检查是否有多个高分意图
            high_scores = [score for score in scores.values() if score > 0.4]
            return len(high_scores) >= 2
        return False
    
    def _calculate_mixed_confidence(self, scores: Dict[IntentType, float]) -> float:
        """计算复合意图的置信度"""
        # 取最高的两个得分的平均值
        sorted_scores = sorted(scores.values(), reverse=True)
        if len(sorted_scores) >= 2:
            return (sorted_scores[0] + sorted_scores[1]) / 2
        return max(scores.values()) if scores else 0.5
    
    def _generate_reasoning(self, 
                          intent: IntentType, 
                          keyword_scores: Dict[IntentType, float],
                          context_scores: Dict[IntentType, float],
                          file_scores: Dict[IntentType, float],
                          has_files: bool,
                          user_input: str) -> str:
        """生成分类依据说明"""
        reasons = []
        
        # 基于关键词的推理
        if intent in keyword_scores and keyword_scores[intent] > 0.3:
            reasons.append(f"包含{intent.value.lower()}相关关键词")
        
        # 基于文件的推理
        if has_files and intent in [IntentType.ANALYSIS, IntentType.PLOTTING]:
            reasons.append("用户上传了文件")
        
        # 基于上下文的推理
        if intent in context_scores and context_scores[intent] > 0.3:
            reasons.append("与历史对话上下文连贯")
        
        # 基于消息特征的推理
        if len(user_input) < 30 and intent == IntentType.GREETING:
            reasons.append("消息简短，类似问候语")
        elif len(user_input) > 100 and intent in [IntentType.ANALYSIS, IntentType.MIXED]:
            reasons.append("消息较长且复杂")
        
        if not reasons:
            reasons.append("基于综合特征判断")
        
        return "; ".join(reasons)


# 创建全局分类器实例
intent_classifier = AdvancedIntentClassifier()


def classify_user_intent(user_input: str, 
                        has_files: bool,
                        context_history: Optional[List[str]] = None,
                        previous_intent: Optional[str] = None) -> IntentResult:
    """
    用户意图分类的便捷函数
    
    Args:
        user_input: 用户输入
        has_files: 是否有文件上传
        context_history: 对话历史 
        previous_intent: 上一轮意图（字符串格式）
        
    Returns:
        IntentResult: 分类结果
    """
    # 转换字符串格式的上一轮意图
    prev_intent_enum = None
    if previous_intent:
        try:
            prev_intent_enum = IntentType(previous_intent)
        except ValueError:
            pass
    
    return intent_classifier.classify(
        user_input=user_input,
        has_files=has_files, 
        context_history=context_history,
        previous_intent=prev_intent_enum
    ) 