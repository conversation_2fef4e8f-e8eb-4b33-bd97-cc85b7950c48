"""
基于LLM的智能意图分类器 - 大幅提升意图识别准确性
"""

import json
import re
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from langchain_openai import ChatOpenAI

from .intent_classifier import IntentType, IntentResult


class LLMIntentClassifier:
    """基于LLM的意图分类器"""
    
    def __init__(self, llm_model: ChatOpenAI):
        """
        初始化LLM意图分类器
        
        Args:
            llm_model: 语言模型实例
        """
        self.llm_model = llm_model
        self._init_classification_prompt()
    
    def _init_classification_prompt(self):
        """初始化意图分类的prompt模板"""
        self.classification_prompt = """分析用户意图，选择合适的类型：

意图类型：
- GREETING: 问候、感谢、告别
- QUESTION: 询问概念、方法、工具使用
- ANALYSIS: 数据分析、统计、处理（不含绘图）
- PLOTTING: 创建图表、可视化
- MIXED: 同时需要分析和绘图

用户输入："{user_input}"
上传文件：{has_files}
上一轮意图：{previous_intent}

返回JSON格式：
{{
    "intent": "类型",
    "confidence": 0.85,
    "reasoning": "分析依据"
}}"""

    def classify(self, 
                user_input: str, 
                has_files: bool = False,
                context_history: Optional[List[str]] = None,
                previous_intent: Optional[str] = None) -> IntentResult:
        """
        使用LLM进行意图分类
        
        Args:
            user_input: 用户输入
            has_files: 是否有文件上传
            context_history: 对话历史上下文
            previous_intent: 上一轮的意图类型
            
        Returns:
            IntentResult: 分类结果
        """
        try:
            # 构建简化的prompt
            full_prompt = self.classification_prompt.format(
                user_input=user_input,
                has_files="是" if has_files else "否",
                previous_intent=previous_intent if previous_intent else "无"
            )
            
            print(f"🤖 LLM意图分析中...")
            
            # 调用LLM进行分类
            response = self.llm_model.invoke(full_prompt)
            response_text = response.content if hasattr(response, 'content') else str(response)
            
            # 解析LLM响应
            result = self._parse_llm_response(response_text, user_input)
            
            print(f"🎯 LLM意图分类完成: {result.intent.value} (置信度: {result.confidence:.2f})")
            
            return result
            
        except Exception as e:
            print(f"⚠️ LLM意图分类失败，使用备用分类器: {str(e)}")
            # 如果LLM分类失败，回退到基础分类器
            from .intent_classifier import intent_classifier
            return intent_classifier.classify(user_input, has_files, context_history, 
                                            IntentType(previous_intent) if previous_intent else None)
    
    def _parse_llm_response(self, response_text: str, user_input: str) -> IntentResult:
        """
        解析LLM的响应结果
        
        Args:
            response_text: LLM响应文本
            user_input: 原始用户输入
            
        Returns:
            IntentResult: 解析后的分类结果
        """
        try:
            # 提取JSON部分
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
            if not json_match:
                raise ValueError("LLM响应中未找到JSON格式")
            
            json_str = json_match.group(0)
            result_data = json.loads(json_str)
            
            # 验证必要字段
            if 'intent' not in result_data:
                raise ValueError("LLM响应缺少intent字段")
            
            # 解析意图
            intent_str = result_data['intent']
            try:
                intent = IntentType(intent_str)
            except ValueError:
                print(f"⚠️ 无效的意图类型: {intent_str}，默认为QUESTION")
                intent = IntentType.QUESTION
            
            # 解析置信度
            confidence = float(result_data.get('confidence', 0.7))
            confidence = max(0.0, min(1.0, confidence))  # 确保在[0,1]范围内
            
            # 解析推理依据
            reasoning = result_data.get('reasoning', 'LLM智能分析')
            
            # 生成简化的子意图得分
            sub_intents = {intent: confidence}
            for other_intent in IntentType:
                if other_intent != intent:
                    sub_intents[other_intent] = max(0.0, confidence - 0.4)
            
            return IntentResult(
                intent=intent,
                confidence=confidence,
                reasoning=f"LLM智能分析: {reasoning}",
                sub_intents=sub_intents
            )
            
        except Exception as e:
            print(f"⚠️ 解析LLM响应失败: {str(e)}")
            # 解析失败时的默认处理
            return self._create_fallback_result(user_input)
    
    def _create_fallback_result(self, user_input: str) -> IntentResult:
        """
        创建备用分类结果
        
        Args:
            user_input: 用户输入
            
        Returns:
            IntentResult: 备用分类结果
        """
        # 简单的关键词判断作为备用
        input_lower = user_input.lower()
        
        if any(word in input_lower for word in ['你好', 'hello', 'hi', '谢谢', '再见', '好的']):
            intent = IntentType.GREETING
            confidence = 0.7
        elif any(word in input_lower for word in ['画', '绘图', '图表', '可视化', '柱状图', '热力图', '折线图']):
            intent = IntentType.PLOTTING
            confidence = 0.7
        elif any(word in input_lower for word in ['分析', '统计', '计算', '处理', '清洗', '汇总']):
            intent = IntentType.ANALYSIS
            confidence = 0.7
        elif '分析' in input_lower and any(word in input_lower for word in ['画', '图']):
            intent = IntentType.MIXED
            confidence = 0.7
        else:
            intent = IntentType.QUESTION
            confidence = 0.6
        
        sub_intents = {intent: confidence}
        for other_intent in IntentType:
            if other_intent != intent:
                sub_intents[other_intent] = max(0.1, confidence - 0.4)
        
        return IntentResult(
            intent=intent,
            confidence=confidence,
            reasoning="备用关键词分析",
            sub_intents=sub_intents
        )


def create_llm_intent_classifier(llm_model: ChatOpenAI) -> LLMIntentClassifier:
    """
    创建LLM意图分类器实例
    
    Args:
        llm_model: 语言模型实例
        
    Returns:
        LLMIntentClassifier: LLM意图分类器实例
    """
    return LLMIntentClassifier(llm_model) 