import re
from langchain.prompts import String<PERSON>romptTemplate
from typing import List, Union
from langchain.agents import <PERSON><PERSON>, AgentExecutor, LLMSingleActionAgent, AgentOutputParser, initialize_agent
from langchain.schema import AgentAction, AgentFinish, OutputParserException

from utils import red_log

class CustomOutputParser(AgentOutputParser):

    def parse(self, llm_output: str) -> Union[AgentAction, AgentFinish]:
        print("\n [LLM 原始输出] ↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓")
        print(llm_output)
        print(" [End of LLM Output] ↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑\n")
        red_log("接收到LLM输出并解析")
        
        # Check if agent should finish - 支持中文冒号和英文冒号
        final_answer_pattern = r"最终答案[：:]"
        match = re.search(final_answer_pattern, llm_output)
        if match:
            # 找到匹配位置，提取后面的内容
            start_pos = match.end()
            final_answer = llm_output[start_pos:].strip()
            return AgentFinish(
                # Return values is generally always a dictionary with a single `output` key
                # It is not recommended to try anything else at the moment :)
                return_values={"output": final_answer},
                log=llm_output,
            )
        # Parse out the action and action input
        regex = r"动作\s*\d*\s*：(.*?)\动作\s*\d*\s*输入\s*\d*\s*：[\s]*(.*)"
        match = re.search(regex, llm_output, re.DOTALL)
        if not match:
            raise OutputParserException(f"Could not parse LLM output: `{llm_output}`")
        action = match.group(1).strip()
        action_input = match.group(2)
        
        # 支持多种代码块格式：```python, ```py
        code_patterns = [
            r"```python\s*(.*?)\s*```",  # ```python...```
            r"```py\s*(.*?)\s*```",      # ```py...```
        ]
        
        action_input_stripped = action_input.strip(" ").strip('"')
        extracted_code = None
        
        for pattern in code_patterns:
            matches = re.findall(pattern, action_input_stripped, re.DOTALL)
            if matches:
                extracted_code = matches[0].strip()
                break
        
        if extracted_code is None:
            # 如果没有找到代码块，抛出异常
            raise OutputParserException(f"Could not find code block in action input: `{action_input_stripped}`")
        else:
            action_input = extracted_code
        # Return the action and action input
        return AgentAction(tool=action, tool_input=action_input, log=extracted_code)