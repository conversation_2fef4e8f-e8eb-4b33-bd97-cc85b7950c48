"""
Agent管理器 - 负责路由用户请求到合适的专门Agent
"""

from typing import Dict, List, Optional, Any, Union
from enum import Enum
from langchain_openai import ChatOpenAI

from .intent_classifier import classify_user_intent, IntentType, IntentResult
from .llm_intent_classifier import create_llm_intent_classifier, LLMIntentClassifier
from .agents import BaseAgent, ChatAgent, AnalysisAgent, PlottingAgent


class AgentType(Enum):
    """Agent类型枚举"""
    CHAT = "chat"
    ANALYSIS = "analysis"  
    PLOTTING = "plotting"


class AgentManager:
    """Agent管理器 - 多Agent架构的核心控制器"""
    
    def __init__(self, llm_model: ChatOpenAI, plotting_tools: List, analysis_tools: List, chat_tools: List, database=None, memory_manager=None, enable_llm_intent: bool = True):
        """
        初始化Agent管理器
        
        Args:
            llm_model: 语言模型实例
            plotting_tools: 绘图工具列表
            analysis_tools: 数据分析工具列表
            chat_tools: 对话工具列表
            database: 数据库实例，用于持久化绘图代码
            memory_manager: 内存管理器实例
            enable_llm_intent: 是否启用LLM智能意图分类器（默认启用）
        """
        self.llm_model = llm_model
        self.plotting_tools = plotting_tools
        self.analysis_tools = analysis_tools
        self.chat_tools = chat_tools
        self.database = database
        self.memory_manager = memory_manager
        self.enable_llm_intent = enable_llm_intent
        
        # 初始化LLM意图分类器（如果启用）
        if self.enable_llm_intent:
            self.llm_intent_classifier = create_llm_intent_classifier(llm_model)
            print("🧠 LLM智能意图分类器已启用")
        else:
            self.llm_intent_classifier = None
            print("📊 使用基础关键词意图分类器")
        
        # 初始化不同类型的Agent实例
        self._init_agents()
        
        # 性能统计
        self.intent_classification_stats = {
            'total_requests': 0,
            'llm_classifications': 0,
            'fallback_classifications': 0,
            'low_confidence_count': 0
        }
        
        # 意图到Agent的映射
        self.intent_to_agent = {
            IntentType.GREETING: AgentType.CHAT,
            IntentType.QUESTION: AgentType.CHAT,
            IntentType.ANALYSIS: AgentType.ANALYSIS,
            IntentType.PLOTTING: AgentType.PLOTTING,
            IntentType.MIXED: AgentType.PLOTTING  # 复合需求优先使用绘图Agent
        }
        
        print("🚀 AgentManager 初始化完成 - 多Agent架构已就绪")
    
    def _init_agents(self):
        """初始化各种专门Agent"""
        print("🔧 正在初始化专用Agent...")
        
        self.agents = {
            AgentType.CHAT: ChatAgent(self.llm_model, self.chat_tools, self.database),
            AgentType.ANALYSIS: AnalysisAgent(self.llm_model, self.analysis_tools, self.database),
            AgentType.PLOTTING: PlottingAgent(self.llm_model, self.plotting_tools, self.database)
        }
        
        print(f"✅ 已初始化 {len(self.agents)} 个专用Agent")
        print(f"   📊 PlottingAgent: {len(self.plotting_tools)} 个绘图工具")
        print(f"   🔬 AnalysisAgent: {len(self.analysis_tools)} 个分析工具")  
        print(f"   💬 ChatAgent: {len(self.chat_tools)} 个对话工具")
    
    def route_request(self, 
                     user_input: str,
                     session_id: str = "default",
                     user_id: str = "default_user",
                     has_files: bool = False,
                     context: Optional[Dict] = None) -> Dict[str, Any]:
        """
        根据意图路由请求到相应Agent
        
        Args:
            user_input: 用户输入
            session_id: 会话ID
            user_id: 用户ID
            has_files: 是否有文件上传
            context: 额外上下文信息
        Returns:
            Dict: 处理结果，包含Agent响应和元信息
        """
        try:
            print(f"🎯 AgentManager 开始处理请求 (Session: {session_id})")
            
            # 从session_id中提取正确的message_id (session_id格式: "user_id:message_id")
            actual_message_id = session_id.split(":", 1)[1] if ":" in session_id else session_id
            
            # 获取会话历史和上下文（使用memory_manager）
            history = context['user_memory']
            # 从ConversationBufferMemory中提取对话历史
            context_history = []
            if hasattr(history, 'chat_memory') and hasattr(history.chat_memory, 'messages'):
                messages = history.chat_memory.messages
                # 提取最近3轮对话的内容
                for msg in messages[-3:]:  # 最近6条消息（3轮对话）
                    if hasattr(msg, 'content'):
                        context_history.append(msg.content)
            
            # 执行意图分类（根据配置选择分类器）
            intent_result = self._classify_intent_intelligently(
                user_input=user_input,
                has_files=has_files,
                context_history=context_history,  # 最近3轮对话
                previous_intent=None
            )
            
            print(f"🧠 意图分析结果: {intent_result.intent.value} (置信度: {intent_result.confidence:.2f})")
            print(f"📝 分析依据: {intent_result.reasoning}")
            
            # 选择合适的Agent
            target_agent_type = self._select_agent(intent_result, context)
            target_agent = self.agents[target_agent_type]
            
            print(f"🤖 选中Agent: {target_agent.agent_name}")
            
            # 准备Agent上下文
            agent_context = self._prepare_agent_context(intent_result, context)
            
            # 执行Agent处理
            result = target_agent.process_request(user_input, agent_context)
            
            # 更新会话状态
            self._update_session_state(user_id, actual_message_id, user_input, result, intent_result)
            
            # 构建最终响应
            response = self._build_response(result, intent_result, target_agent_type)
            
            print(f"✅ AgentManager 处理完成")
            return response
            
        except Exception as e:
            import traceback
            traceback.print_exc()
            print(f"❌ AgentManager 处理失败: {str(e)}")
            return self._build_error_response(str(e), user_input)

    def _select_agent(self, 
                     intent_result: IntentResult, 
                     context: Optional[Dict] = None) -> AgentType:
        """
        选择合适的Agent
        
        Args:
            intent_result: 意图分析结果
            context: 上下文信息
            
        Returns:
            AgentType: 选中的Agent类型
        """
        # 基本意图映射
        base_agent = self.intent_to_agent.get(intent_result.intent, AgentType.CHAT)
        
        # 特殊情况处理
        if context:
            # 如果有文件上传，优先考虑分析或绘图
            if context.get('has_files', False):
                if intent_result.intent == IntentType.GREETING:
                    # 有文件但是问候，可能是想开始数据分析
                    base_agent = AgentType.ANALYSIS

        
        # 低置信度时的保守策略
        if intent_result.confidence < 0.5:
            print(f"⚠️ 意图置信度较低 ({intent_result.confidence:.2f})，使用对话Agent")
            base_agent = AgentType.CHAT
        
        return base_agent
    
    def _prepare_agent_context(self, 
                              intent_result: IntentResult,
                              context: Dict) -> Dict[str, Any]:
        """准备Agent执行上下文"""
        # 基础上下文信息
        agent_context = {
            'intent': intent_result.intent.value,
            'confidence': intent_result.confidence,
            'sub_intents': intent_result.sub_intents
        }
        
        # 合并传入的上下文（包含session_id, user_id, message_id, user_memory等）
        agent_context.update(context)
        
        # 添加历史对话信息
        if 'user_memory' in context and context['user_memory']:
            history_messages = []
            if hasattr(context['user_memory'], 'chat_memory') and hasattr(context['user_memory'].chat_memory, 'messages'):
                messages = context['user_memory'].chat_memory.messages
                # 提取最近10轮对话的内容
                for msg in messages[-10:]:  # 最近10条消息
                    if hasattr(msg, 'content'):
                        history_messages.append(msg.content)
            agent_context['history'] = '\n'.join(history_messages)  # 最近10轮对话
        else:
            agent_context['history'] = ''
        
        # 从memory_manager获取用户状态（如果context中没有）
        if self.memory_manager and 'user_id' in context and 'message_id' in context:
            try:
                user_state = self.memory_manager.get_user_state(context['user_id'], context['message_id'])
                # 只添加context中没有的状态信息
                for key, value in user_state.items():
                    if key not in agent_context:
                        agent_context[key] = value
            except Exception as e:
                print(f"⚠️ 获取用户状态失败: {str(e)}")
        
        return agent_context
    
    def _update_session_state(self, 
                             user_id: str,
                             message_id: str,
                             user_input: str,
                             result: Dict,
                             intent_result: IntentResult):
        """更新会话状态（使用memory_manager）"""
        if not self.memory_manager:
            return
        
        try:
            # 更新用户状态中的意图信息
            user_state = self.memory_manager.get_user_state(user_id, message_id)
            user_state['last_intent'] = intent_result.intent.value
            user_state['intent_confidence'] = intent_result.confidence
            user_state['last_agent'] = result.get('agent_name', '')
            
            # 保存绘图代码（用于连续修改）
            if result.get('plotting_code'):
                self.memory_manager.update_latest_plotting_code(user_id, message_id, result['plotting_code'])

            # 保存分析结果（用于后续绘图）
            if result.get('analysis_summary'):
                user_state['last_analysis'] = result['analysis_summary']
                
        except Exception as e:
            print(f"❌ 更新会话状态失败: {str(e)}")
    
    def _build_response(self, 
                       result: Dict,
                       intent_result: IntentResult,
                       agent_type: AgentType) -> Dict[str, Any]:
        """构建最终响应"""
        response = {
            'output': result.get('output', ''),
            'success': True,
            'metadata': {
                'agent_type': agent_type.value,
                'agent_name': result.get('agent_name', ''),
                'intent': intent_result.intent.value,
                'confidence': intent_result.confidence,
                'reasoning': intent_result.reasoning,
                'interaction_type': result.get('interaction_type', 'general')
            }
        }
        
        # 添加特定类型的元信息
        if 'suggestions' in result:
            response['suggestions'] = result['suggestions']
        
        if 'visualization_suggestions' in result:
            response['visualization_suggestions'] = result['visualization_suggestions']
        
        if 'chart_info' in result:
            response['chart_info'] = result['chart_info']
        
        if 'analysis_summary' in result:
            response['analysis_summary'] = result['analysis_summary']
        
        # 添加中间步骤（用于调试）
        if result.get('intermediate_steps'):
            response['debug_info'] = {
                'intermediate_steps': len(result['intermediate_steps']),
                'agent_iterations': len(result['intermediate_steps'])
            }
        
        return response
    
    def _build_error_response(self, error_msg: str, user_input: str) -> Dict[str, Any]:
        """构建错误响应"""
        return {
            'output': f"抱歉，处理您的请求时发生了错误：{error_msg}",
            'success': False,
            'error': error_msg,
            'metadata': {
                'agent_type': 'error',
                'intent': 'unknown',
                'confidence': 0.0,
                'user_input': user_input[:100]  # 截断用户输入
            }
        }
    
    def get_session_info(self, session_id: str = "default") -> Dict[str, Any]:
        """获取会话信息"""
        if not self.memory_manager:
            return {
                'session_id': session_id,
                'error': 'memory_manager not available'
            }
        
        try:
            # 从session_id中提取用户信息
            if ":" in session_id:
                user_id, message_id = session_id.split(":", 1)
            else:
                user_id, message_id = "default_user", session_id
            
            # 获取缓存统计
            cache_stats = self.memory_manager.get_cache_stats()
            user_state = self.memory_manager.get_user_state(user_id, message_id)
            
            return {
                'session_id': session_id,
                'user_id': user_id,
                'message_id': message_id,
                'cache_stats': cache_stats,
                'user_state_keys': list(user_state.keys()),
                'available_agents': [agent_type.value for agent_type in self.agents.keys()]
            }
        except Exception as e:
            return {
                'session_id': session_id,
                'error': str(e)
            }
    
    def clear_session(self, session_id: str = "default"):
        """清除会话状态"""
        if not self.memory_manager:
            print(f"⚠️ memory_manager不可用，无法清除会话")
            return
        
        try:
            # 从session_id中提取用户信息
            if ":" in session_id:
                user_id, message_id = session_id.split(":", 1)
            else:
                user_id, message_id = "default_user", session_id
            
            # 清除用户状态
            user_state = self.memory_manager.get_user_state(user_id, message_id)
            user_state.clear()
            user_state.update({
                'current_files': [],
                'current_image_path': None,
                'latest_plotting_code': None,
                'code_generation_count': 0,
                'image_analysis_cache': {}
            })
            
            # 清除内存中的对话
            memory_key = f"{user_id}:{message_id}"
            if memory_key in self.memory_manager.user_memories:
                del self.memory_manager.user_memories[memory_key]
            if memory_key in self.memory_manager.user_states:
                del self.memory_manager.user_states[memory_key]
            if memory_key in self.memory_manager.access_times:
                del self.memory_manager.access_times[memory_key]
            
            print(f"🗑️ 已清除会话 {session_id} 的状态")
        except Exception as e:
            print(f"❌ 清除会话状态失败: {str(e)}")
    
    def get_agent_stats(self) -> Dict[str, Any]:
        """获取Agent统计信息"""
        stats = {}
        for agent_type, agent in self.agents.items():
            stats[agent_type.value] = agent.get_agent_info()
        
        return {
            'agents': stats,
            'memory_manager_available': self.memory_manager is not None,
            'manager_status': 'active'
        } 

    def _classify_intent_intelligently(self, 
                                       user_input: str,
                                       has_files: bool,
                                       context_history: Optional[List[str]] = None,
                                       previous_intent: Optional[str] = None) -> IntentResult:
        """
        智能意图分类：根据配置使用LLM或基础分类器
        
        Args:
            user_input: 用户输入
            has_files: 是否有文件上传
            context_history: 对话历史上下文
            previous_intent: 上一轮意图
            
        Returns:
            IntentResult: 分类结果
        """
        # 更新统计
        self.intent_classification_stats['total_requests'] += 1
        
        if not self.enable_llm_intent or self.llm_intent_classifier is None:
            # 如果未启用LLM分类器，直接使用基础分类器
            self.intent_classification_stats['fallback_classifications'] += 1
            return classify_user_intent(
                user_input=user_input,
                has_files=has_files,
                context_history=context_history,
                previous_intent=previous_intent
            )
        
        try:
            # 使用LLM意图分类器
            result = self.llm_intent_classifier.classify(
                user_input=user_input,
                has_files=has_files,
                context_history=context_history,
                previous_intent=previous_intent
            )
            self.intent_classification_stats['llm_classifications'] += 1
            
            # 检查置信度，如果太低则使用基础分类器作为参考
            if result.confidence < 0.6:
                self.intent_classification_stats['low_confidence_count'] += 1
                print(f"⚠️ LLM意图置信度较低 ({result.confidence:.2f})，结合基础分类器验证...")
                
                # 调用基础分类器进行验证
                fallback_result = classify_user_intent(
                    user_input=user_input,
                    has_files=has_files,
                    context_history=context_history,
                    previous_intent=previous_intent
                )
                
                # 如果两个分类器结果一致，提高置信度
                if fallback_result.intent == result.intent:
                    result.confidence = min(0.8, result.confidence + 0.2)
                    result.reasoning += f" (基础分类器验证一致)"
                    print(f"✅ 两个分类器结果一致，提升置信度至 {result.confidence:.2f}")
                else:
                    print(f"⚠️ 分类器结果不一致: LLM={result.intent.value}, 基础={fallback_result.intent.value}")
                    # 如果LLM置信度很低且结果不一致，可以考虑使用基础分类器结果
                    if result.confidence < 0.4 and fallback_result.confidence > 0.3:
                        print(f"📄 采用基础分类器结果")
                        result = fallback_result
                        result.reasoning = f"LLM低置信度，采用基础分类器: {result.reasoning}"
                        self.intent_classification_stats['fallback_classifications'] += 1
            
            return result
            
        except Exception as e:
            print(f"❌ LLM意图分类完全失败，回退到基础分类器: {str(e)}")
            self.intent_classification_stats['fallback_classifications'] += 1
            # 完全回退到基础分类器
            return classify_user_intent(
                user_input=user_input,
                has_files=has_files,
                context_history=context_history,
                previous_intent=previous_intent
            ) 

    def get_intent_classification_stats(self) -> Dict[str, Any]:
        """
        获取意图分类性能统计
        
        Returns:
            Dict: 性能统计信息
        """
        stats = self.intent_classification_stats.copy()
        
        if stats['total_requests'] > 0:
            stats['llm_success_rate'] = stats['llm_classifications'] / stats['total_requests']
            stats['fallback_rate'] = stats['fallback_classifications'] / stats['total_requests']
            stats['low_confidence_rate'] = stats['low_confidence_count'] / stats['total_requests']
        else:
            stats['llm_success_rate'] = 0
            stats['fallback_rate'] = 0
            stats['low_confidence_rate'] = 0
        
        stats['llm_enabled'] = self.enable_llm_intent
        
        return stats
    
    def toggle_llm_intent_classifier(self, enable: bool):
        """
        动态切换LLM意图分类器开关
        
        Args:
            enable: 是否启用LLM意图分类器
        """
        if enable and not self.enable_llm_intent:
            # 启用LLM分类器
            if self.llm_intent_classifier is None:
                self.llm_intent_classifier = create_llm_intent_classifier(self.llm_model)
            self.enable_llm_intent = True
            print("🧠 LLM智能意图分类器已启用")
            
        elif not enable and self.enable_llm_intent:
            # 禁用LLM分类器
            self.enable_llm_intent = False
            print("📊 已切换到基础关键词意图分类器")
    
    def reset_stats(self):
        """重置性能统计"""
        self.intent_classification_stats = {
            'total_requests': 0,
            'llm_classifications': 0,
            'fallback_classifications': 0,
            'low_confidence_count': 0
        }
        print("📊 意图分类统计已重置") 

 