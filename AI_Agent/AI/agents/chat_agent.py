"""
对话Agent - 专门处理问候、咨询等对话类需求
"""

from typing import Dict, List, Optional, Any
from .base_agent import BaseAgent


class ChatAgent(BaseAgent):
    """对话Agent - 处理问候、一般咨询等简单对话需求"""
    
    def __init__(self, llm_model, tools, database=None):
        super().__init__(llm_model, tools, "ChatAgent", "GREETING", database)
    
    def get_max_iterations(self) -> int:
        """对话Agent只需要1次迭代"""
        return 2
    
    def preprocess_input(self, user_input: str, context: Optional[Dict] = None) -> str:
        """预处理对话输入"""
        # 对话类请求不需要特殊预处理
        return user_input
    
    def postprocess_result(self, result: Dict, context: Optional[Dict] = None) -> Dict[str, Any]:
        """后处理对话结果"""
        processed_result = super().postprocess_result(result, context)
        
        # 为对话结果添加额外标识
        processed_result['interaction_type'] = 'chat'
        processed_result['suggestions'] = self._generate_suggestions(result.get('output', ''))
        
        return processed_result
    
    def _generate_suggestions(self, output: str) -> List[str]:
        """根据回复内容生成后续建议"""
        suggestions = []
        
        output_lower = output.lower()
        
        # 根据回复内容提供智能建议
        if '数据' in output_lower or '分析' in output_lower:
            suggestions.append("💡 如需进行数据分析，可以上传您的数据文件")
        
        if '可视化' in output_lower or '图' in output_lower:
            suggestions.append("📊 如需创建图表，可以告诉我您想要的图表类型")
        
        # if '服务' in output_lower or '产品' in output_lower:
        #     suggestions.append("📋 可以询问更多关于迈维代谢服务的详细信息")
        
        if not suggestions:
            suggestions.append("❓ 还有其他问题吗？我很乐意为您解答")
        
        return suggestions 