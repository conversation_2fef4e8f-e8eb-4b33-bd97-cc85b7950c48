"""
分析Agent - 专门处理数据分析相关需求
"""

from typing import Dict, List, Optional, Any
from .base_agent import BaseAgent


class AnalysisAgent(BaseAgent):
    """分析Agent - 专门处理数据分析、统计计算等需求"""
    
    def __init__(self, llm_model, tools, database=None):
        super().__init__(llm_model, tools, "AnalysisAgent", "ANALYSIS", database)
    
    def get_max_iterations(self) -> int:
        """分析Agent可能需要多次迭代来完成复杂分析"""
        return 3
    
    def preprocess_input(self, user_input: str, context: Optional[Dict] = None) -> str:
        """预处理分析输入"""
        # 分析类请求不需要特殊预处理
        return user_input
    
    def postprocess_result(self, result: Dict, context: Optional[Dict] = None) -> Dict[str, Any]:
        """后处理分析结果"""
        processed_result = super().postprocess_result(result, context)
        
        # 为分析结果添加额外信息
        processed_result['interaction_type'] = 'analysis'
        processed_result['analysis_summary'] = self._extract_analysis_summary(result.get('output', ''))
        #processed_result['visualization_suggestions'] = self._suggest_visualizations(result.get('output', ''))
        
        return processed_result
    
    def _extract_analysis_summary(self, output: str) -> Dict[str, Any]:
        """从分析输出中提取关键结果摘要"""
        summary = {
            'data_shape': None,
            'key_statistics': [],
            'correlations': [],
            'missing_values': None
        }
        
        # 简单的关键信息提取（可以更复杂）
        lines = output.split('\n')
        for line in lines:
            if 'shape' in line.lower() or '形状' in line:
                summary['data_shape'] = line.strip()
            elif 'mean' in line.lower() or '平均' in line or '均值' in line:
                summary['key_statistics'].append(line.strip())
            elif 'correlation' in line.lower() or '相关' in line:
                summary['correlations'].append(line.strip())
        
        return summary
    
    def _suggest_visualizations(self, output: str) -> List[str]:
        """根据分析结果建议合适的可视化"""
        suggestions = []
        
        output_lower = output.lower()
        
        # 根据分析类型建议可视化（提醒使用绘图Agent）
        if 'correlation' in output_lower or '相关' in output_lower:
            suggestions.append("📊 建议使用绘图Agent创建相关性热力图来可视化变量间关系")
        
        if 'distribution' in output_lower or '分布' in output_lower:
            suggestions.append("📈 建议使用绘图Agent创建直方图或密度图显示数据分布")
        
        if 'group' in output_lower or '分组' in output_lower:
            suggestions.append("📊 建议使用绘图Agent创建分组柱状图或箱线图进行比较")
        
        if 'trend' in output_lower or '趋势' in output_lower or 'time' in output_lower:
            suggestions.append("📉 建议使用绘图Agent创建折线图显示时间趋势")
        
        if not suggestions:
            suggestions.append("📊 根据分析结果，可以考虑使用绘图Agent创建相应的图表进行可视化")
        
        return suggestions 