"""
数据库管理模块 - 包含聊天记录的数据库操作
"""
import sqlite3
import json
from typing import List, Optional
from threading import Lock
from utils import get_beijing_time
from config import DEFAULT_DB_PATH

class ChatDatabase:
    """聊天数据库管理类"""
    
    def __init__(self, db_path: str = DEFAULT_DB_PATH):
        self.db_path = db_path
        self.lock = Lock()
        self.init_database()
    
    def init_database(self):
        """初始化数据库表"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 创建表，增加image_path字段
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS chat_messages (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT NOT NULL,
                    message_id TEXT NOT NULL,
                    role TEXT NOT NULL,
                    content TEXT NOT NULL,
                    file_ids TEXT,
                    image_path TEXT,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(user_id, message_id, id)
                )
            """)
            cursor.execute("""
                CREATE INDEX IF NOT EXISTS idx_user_message 
                ON chat_messages(user_id, message_id)
            """)
            conn.commit()
    
    def save_message(self, user_id: str, message_id: str, role: str, content: str, file_ids: List[str] = None, image_path: str = None):
        """保存单条消息到数据库"""
        with self.lock:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                file_ids_json = json.dumps(file_ids) if file_ids else None
                beijing_time = get_beijing_time()
                cursor.execute("""
                    INSERT INTO chat_messages (user_id, message_id, role, content, file_ids, image_path, timestamp)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (user_id, message_id, role, content, file_ids_json, image_path, beijing_time))
                conn.commit()
    
    def load_messages(self, user_id: str, message_id: str) -> List[dict]:
        """从数据库加载指定对话的消息"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT role, content, file_ids, image_path FROM chat_messages
                WHERE user_id = ? AND message_id = ?
                ORDER BY timestamp ASC
            """, (user_id, message_id))
            
            messages = []
            for row in cursor.fetchall():
                role, content, file_ids_json, image_path = row
                file_ids = json.loads(file_ids_json) if file_ids_json else None
                messages.append({
                    "role": role,
                    "content": content,
                    "file_ids": file_ids,
                    "image_path": image_path
                })
            return messages

    def load_messages_for_api(self, user_id: str, message_id: str) -> List[dict]:
        """从数据库加载指定对话的消息（API专用，过滤掉system消息）"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT role, content, file_ids, image_path FROM chat_messages
                WHERE user_id = ? AND message_id = ? AND role != 'system'
                ORDER BY timestamp ASC
            """, (user_id, message_id))
            
            messages = []
            for row in cursor.fetchall():
                role, content, file_ids_json, image_path = row
                file_ids = json.loads(file_ids_json) if file_ids_json else None
                message_data = {
                    "role": role,
                    "content": content,
                    "file_ids": file_ids
                }
                # 只有当image_path存在时才添加到返回数据中
                if image_path:
                    message_data["image_path"] = image_path
                messages.append(message_data)
            return messages
    
    # def get_user_conversations(self, user_id: str) -> List[str]:
    #     """获取用户的所有对话ID列表"""
    #     with sqlite3.connect(self.db_path) as conn:
    #         cursor = conn.cursor()
    #         cursor.execute("""
    #             SELECT DISTINCT message_id FROM chat_messages
    #             WHERE user_id = ?
    #             ORDER BY timestamp DESC
    #         """, (user_id,))
    #         return [row[0] for row in cursor.fetchall()]
    
    def get_user_conversations_with_details(self, user_id: str) -> List[dict]:
        """获取用户的所有对话列表及详细信息"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT 
                    message_id,
                    MIN(timestamp) as created_at,
                    MAX(timestamp) as last_update,
                    COUNT(*) as message_count
                FROM chat_messages
                WHERE user_id = ?
                GROUP BY message_id
                ORDER BY last_update DESC
            """, (user_id,))
            
            conversations = []
            for row in cursor.fetchall():
                message_id, created_at, last_update, message_count = row
                
                # 获取第一条用户消息作为标题
                cursor.execute("""
                    SELECT content FROM chat_messages
                    WHERE user_id = ? AND message_id = ? AND role = 'human'
                    ORDER BY timestamp ASC
                    LIMIT 1
                """, (user_id, message_id))
                title_row = cursor.fetchone()
                title = title_row[0][:15] + "..." if title_row and len(title_row[0]) > 15 else (title_row[0] if title_row else "新对话")
                
                # 获取最后一条消息作为预览（排除system消息）
                cursor.execute("""
                    SELECT content, role FROM chat_messages
                    WHERE user_id = ? AND message_id = ? AND role != 'system'
                    ORDER BY timestamp DESC
                    LIMIT 1
                """, (user_id, message_id))
                last_msg_row = cursor.fetchone()
                if last_msg_row:
                    last_content = last_msg_row[0]
                    preview = last_content[:50] + "..." if len(last_content) > 50 else last_content
                else:
                    preview = "暂无消息"
                
                conversations.append({
                    "id": message_id,
                    "title": title,
                    "lastMessage": preview,
                    "lastUpdate": last_update,
                    "createdAt": created_at,
                    "messageCount": message_count
                })
            
            return conversations
    
    def delete_conversation(self, user_id: str, message_id: str) -> bool:
        """删除指定的对话"""
        with self.lock:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    DELETE FROM chat_messages
                    WHERE user_id = ? AND message_id = ?
                """, (user_id, message_id))
                deleted_rows = cursor.rowcount
                conn.commit()
                return deleted_rows > 0

    def delete_system_messages_by_type(self, user_id: str, message_id: str, message_type: str):
        """从数据库中删除特定类型的系统消息"""
        with self.lock:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 根据消息类型构建删除条件
                if message_type == "file_path":
                    cursor.execute("""
                        DELETE FROM chat_messages
                        WHERE user_id = ? AND message_id = ? AND role = 'system' 
                        AND content LIKE '%<file_path>%'
                    """, (user_id, message_id))
                elif message_type == "image_path":
                    cursor.execute("""
                        DELETE FROM chat_messages
                        WHERE user_id = ? AND message_id = ? AND role = 'system' 
                        AND content LIKE '%图像保存路径%'
                    """, (user_id, message_id))
                elif message_type == "image_style":
                    cursor.execute("""
                        DELETE FROM chat_messages
                        WHERE user_id = ? AND message_id = ? AND role = 'system' 
                        AND content LIKE '%绘图样式参考图%'
                    """, (user_id, message_id))
                elif message_type == "latest_code":
                    # 更全面地清理绘图代码消息，使用多个匹配条件
                    cursor.execute("""
                        DELETE FROM chat_messages
                        WHERE user_id = ? AND message_id = ? AND role = 'system' 
                        AND (content LIKE '%上次生成的绘图代码%' OR content LIKE '%绘图代码（第%')
                    """, (user_id, message_id))
                
                deleted_count = cursor.rowcount
                conn.commit()
                
                if deleted_count > 0:
                    print(f"🗄️ 从数据库清理 {message_type} 类型消息: 删除了 {deleted_count} 条")